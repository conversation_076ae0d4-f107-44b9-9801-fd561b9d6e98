"""
网页内容提取API使用示例
演示如何调用Flask API接口进行网页内容提取
"""

import requests
import json
import time

# API服务地址
API_BASE_URL = "http://localhost:5000"

def test_health_check():
    """
    测试健康检查接口
    """
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
        return False

def test_status():
    """
    测试状态信息接口
    """
    print("\n=== 测试状态信息接口 ===")
    try:
        response = requests.get(f"{API_BASE_URL}/status")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取状态信息失败: {str(e)}")
        return False

def test_single_extract():
    """
    测试单个URL内容提取接口
    """
    print("\n=== 测试单个URL内容提取接口 ===")
    
    # 测试数据
    test_data = {
        "url": "https://finance.sina.com.cn/tech/2024/01/15/doc-inaehqvs1234567.shtml",
        "enterprise_name": "新浪科技",
        "enterprise_id": "sina_tech_001"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/extract",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        
        # 打印主要结果信息
        print(f"提取成功: {result.get('success', False)}")
        print(f"实际访问URL: {result.get('实际访问URL', '')}")
        print(f"网页内容长度: {len(result.get('网页内容', ''))}")
        print(f"PDF文件名: {result.get('网页pdf', '')}")
        print(f"内容标签: {result.get('内容标签', '')}")
        print(f"情感极性: {result.get('情感极性', '')}")
        print(f"情感强度: {result.get('情感强度', 0)}")
        
        if not result.get('success', False):
            print(f"错误信息: {result.get('error', '')}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"单个URL提取失败: {str(e)}")
        return False

def test_batch_extract():
    """
    测试批量URL内容提取接口
    """
    print("\n=== 测试批量URL内容提取接口 ===")
    
    # 测试数据
    test_data = {
        "urls": [
            {
                "url": "https://finance.sina.com.cn/tech/2024/01/15/doc-example1.shtml",
                "enterprise_name": "新浪科技",
                "enterprise_id": "sina_tech_001"
            },
            {
                "url": "https://tech.163.com/24/0115/example2.html",
                "enterprise_name": "网易科技",
                "enterprise_id": "netease_tech_001"
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/batch_extract",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        result = response.json()
        
        # 打印批量处理结果
        print(f"批量处理成功: {result.get('success', False)}")
        print(f"总数量: {result.get('total_count', 0)}")
        print(f"成功数量: {result.get('success_count', 0)}")
        print(f"失败数量: {result.get('failed_count', 0)}")
        
        # 打印每个URL的处理结果
        results = result.get('results', [])
        for i, url_result in enumerate(results):
            print(f"\nURL {i+1} 处理结果:")
            print(f"  成功: {url_result.get('success', False)}")
            print(f"  URL: {url_result.get('url', '')}")
            print(f"  内容长度: {len(url_result.get('网页内容', ''))}")
            if not url_result.get('success', False):
                print(f"  错误: {url_result.get('error', '')}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"批量URL提取失败: {str(e)}")
        return False

def test_error_handling():
    """
    测试错误处理
    """
    print("\n=== 测试错误处理 ===")
    
    # 测试空URL
    print("1. 测试空URL:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/extract",
            json={"url": ""},
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"测试空URL失败: {str(e)}")
    
    # 测试无效JSON
    print("\n2. 测试无效请求体:")
    try:
        response = requests.post(
            f"{API_BASE_URL}/extract",
            data="invalid json",
            headers={'Content-Type': 'application/json'}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"测试无效JSON失败: {str(e)}")
    
    # 测试不存在的接口
    print("\n3. 测试不存在的接口:")
    try:
        response = requests.get(f"{API_BASE_URL}/nonexistent")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"测试不存在接口失败: {str(e)}")

def main():
    """
    主测试函数
    """
    print("网页内容提取API测试开始")
    print("=" * 50)
    
    # 等待API服务启动
    print("等待API服务启动...")
    time.sleep(2)
    
    # 执行测试
    tests = [
        ("健康检查", test_health_check),
        ("状态信息", test_status),
        ("单个URL提取", test_single_extract),
        ("批量URL提取", test_batch_extract),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if test_name == "错误处理":
                test_func()  # 错误处理测试不返回布尔值
                results.append((test_name, True))
            else:
                success = test_func()
                results.append((test_name, success))
        except Exception as e:
            print(f"{test_name}测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 打印测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
