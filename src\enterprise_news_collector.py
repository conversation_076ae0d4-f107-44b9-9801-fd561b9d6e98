# -*- coding: utf-8 -*-
"""
企业新闻采集器
本模块提供从搜索引擎采集企业新闻的功能。
支持从百度新闻异步采集，并可将结果保存到Excel和MongoDB。
"""

import traceback
import json
import ast
import requests
from datetime import datetime
import pandas as pd
import os
import sys
import time
import asyncio
import aiohttp


from utils.api_utils import LaungeeAPIUtils
from utils.search_utils import BaiduNewsSearcher
from utils.time_utils import TimeUtils
from utils.data_utils import DataUtils
from utils.MongoClient import monclient
from daili import ProxyOperator

# 将项目根目录添加到路径
root_path = os.path.abspath(os.path.join(os.path.abspath(__file__), '..', '..'))
sys.path.append(root_path)

class EnterpriseNewsCollector:
    """用于从搜索引擎采集企业新闻的类。"""
    
    def __init__(self, use_proxy=True, keep_days=14, keep_notes=True, addition_times=1, concurrent_limit=5, limit=None, output_file=None, save_to_excel=True, save_to_mongodb=True):
        """使用默认设置初始化采集器。

        Args:
            use_proxy (bool, optional): 是否使用阿布云代理池。默认为True。
            keep_days (int, optional): 保留天数，默认为14天。
            keep_notes (bool, optional): 是否保留备注，默认为True。
            addition_times (int, optional): 补采次数，默认为1次。
            concurrent_limit (int, optional): 并发限制，默认为5。
            limit (int, optional): 限制采集的企业数量，用于测试，默认为None（不限制）。
            output_file (str, optional): 输出文件路径，默认为None（自动生成）。
            save_to_excel (bool, optional): 是否保存到Excel文件，默认为True。
            save_to_mongodb (bool, optional): 是否保存到MongoDB数据库，默认为True。
        """
        self.collection_time = datetime.now().strftime('%Y-%m-%d %H-%M-%S')

        self.time_utils = TimeUtils()

        self.log_filename = f'企业动态采集日志_{self.collection_time}.txt'
        # 确保日志目录存在
        self.log_dir = os.path.join(root_path, 'log', '企业动态采集日志')
        os.makedirs(self.log_dir, exist_ok=True)
        self.log_file = os.path.join(self.log_dir, self.log_filename)

        self.keep_days = keep_days  # 保留14天内的结果
        self.keep_notes = keep_notes  # 保留备注
        self.use_proxy_pool = use_proxy  # 是否使用代理池
        self.addition_times = addition_times  # 额外采集次数,总共采集2次
        self.concurrent_limit = concurrent_limit  # 并发限制
        self.limit = limit  # 限制采集的企业数量
        self.save_to_excel = save_to_excel  # 是否保存到Excel文件
        self.save_to_mongodb = save_to_mongodb  # 是否保存到MongoDB数据库

        # 初始化百度搜索器
        self.baidu_searcher = BaiduNewsSearcher(use_proxy=self.use_proxy_pool)

        self.start_time = time.perf_counter()

        # 设置输出文件路径
        if output_file:
            # 如果提供了输出文件路径，使用该路径
            if os.path.isabs(output_file):
                self.output_file = output_file
            else:
                # 如果是相对路径，相对于项目根目录
                self.output_file = os.path.join(root_path, output_file)
            # 确保输出目录存在
            os.makedirs(os.path.dirname(self.output_file), exist_ok=True)
        else:
            # 如果没有提供输出文件路径，使用默认路径
            self.output_filename = f'企业动态采集结果_{self.collection_time}.xlsx'
            self.output_dir = os.path.join(root_path, 'output')
            os.makedirs(self.output_dir, exist_ok=True)
            self.output_file = os.path.join(self.output_dir, self.output_filename)

        # 全局计数器
        self.total_count = 0
        self.current_count = 0
    
    
    async def crawl_single_enterprise_news(self, result_data, company, semaphore, proxy, idx):
        """异步从百度新闻爬取单个公司的新闻。

        Args:
            result_data (list): 存储爬取结果的列表
            company (dict): 包含公司信息的字典，带有'nameCn'和'enterpriseId'字段
            semaphore (asyncio.Semaphore): 用于限制并发的信号量
            proxy (object): 用于发送请求的代理对象，如果不使用代理则为None
            idx (int): 当前采集索引
        """
        company_name = company['nameCn']
        company_id = company['enterpriseId']
        save_data = []

        # 检查代理池状态（如果使用代理）
        if self.use_proxy_pool and proxy:
            proxy.check_proxy_pool()

        async with semaphore:
            # 执行多次采集尝试
            for attempt in range(self.addition_times + 1):
                try:
                    await asyncio.sleep(1)  # 确保每个任务至少需要1秒，以限制每秒请求数

                    # 使用新的百度搜索器进行异步搜索
                    search_results = await self.baidu_searcher.async_search(
                        company_name,
                        max_retries=3,
                        proxy_operator=proxy if self.use_proxy_pool else None
                    )

                    if search_results:
                        # 将搜索结果转换为原有格式
                        for result in search_results:
                            # 解析时间
                            save_data.append([
                                company_id,
                                '百度资讯',
                                company_name,
                                result['title'],
                                result['summary'],
                                result['url'],
                                result['source'],
                                result['publish_time']
                            ])
                        break
                    else:
                        # 如果没有搜索结果，添加空记录
                        save_data.append([company_id, '百度资讯', company_name, '', '', '', '', ''])
                        break

                except asyncio.TimeoutError:
                    pass
                except aiohttp.ClientHttpProxyError:
                    if self.use_proxy_pool and proxy:
                        proxy.check_proxy_pool()  # 检查代理池是否已过期
                except Exception as e:
                    pass
            else:
                # 如果所有尝试都失败，添加空记录
                save_data.append([company_id, '百度资讯', company_name, '', '', '', '', ''])

            # 创建DataFrame并根据需要按日期过滤
            save_df = pd.DataFrame(data=save_data, columns=['企业id', '采集来源', '企业名', '标题', '摘要', '标题链接', '来源', '发布时间'])
            
            if self.keep_days > 0 and not save_df.empty:  # 根据发布日期过滤
                date_mask = save_df['发布时间'].apply(lambda x: DataUtils.within_days(x, self.keep_days))
                save_df = save_df.loc[date_mask].reset_index(drop=True)
            
            # 过滤结果，要求企业名在标题或摘要中
            if not save_df.empty:
                mask = save_df.apply(lambda x: DataUtils.has_name_in_content(x['企业名'], x['标题'], x['摘要']), axis=1)
                save_df = save_df.loc[mask].reset_index(drop=True)

            # 删除重复项
            save_df = save_df.drop_duplicates(subset=['企业名', '摘要'])
            save_df = save_df.drop_duplicates(subset=['企业名', '标题'])

            # 更新当前计数
            self.current_count += len(save_df)

            # 将结果添加到结果数据列表
            for index, row in save_df.iterrows():
                result_data.append(row.tolist())

            # 打印采集进度
            used_time = time.perf_counter() - self.start_time
            print('采集进度:{}/{}，用时{}分{}秒，采集数量：{} ,总数量：{}   '.format(
                idx + 1, self.total_count, int(used_time // 60), int(used_time % 60), len(save_df), self.current_count))

    async def crawl_enterprise_news(self, companies):
        """异步采集多个公司的新闻。
        
        Args:
            companies (list): 包含'nameCn'和'enterpriseId'字段的公司字典列表
            
        Returns:
            DataFrame: 包含采集到的新闻数据的DataFrame
        """
        semaphore = asyncio.Semaphore(self.concurrent_limit)  # 限制并发数
        output_list = []
        
        # 根据配置决定是否初始化代理
        proxy = None
        if self.use_proxy_pool:
            proxy = ProxyOperator('HT99W04OL07C735D', 'A767B2BC2CB312EE')
            print(f"已初始化阿布云代理池")
        else:
            print(f"未使用代理池，将直接连接")
        
        # 打开日志文件
        with open(self.log_file, 'a+', encoding='utf-8') as file:
            print(f'即将采集：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}', file=file)
            print(f'采集完毕后补采{self.addition_times}次，共采集{self.addition_times + 1}次。'.replace('补采0次', '不进行补采'))
            
            try:
                print(f'\n正在采集 {len(companies)} 家企业 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}:', file=file)
                print(f'\n正在采集 {len(companies)} 家企业')

                self.total_count = len(companies) 

                # 创建所有企业的采集任务
                tasks = []
                for index, company in enumerate(companies):
                    tasks.append(asyncio.create_task(
                        self.crawl_single_enterprise_news(output_list, company, semaphore, proxy, index)))

                # 并发执行所有任务
                await asyncio.wait(tasks)

                if len(output_list) == 0:
                    print(f'没有采集到信息, 总用时{int((time.perf_counter() - self.start_time) // 60)}分', file=file)
                    return pd.DataFrame()

            except Exception as e:
                print(f'采集失败，失败信息：{traceback.format_exc()}', file=file)
                return pd.DataFrame()
            else:
                used_time = int(time.perf_counter() - self.start_time)
                print(f"采集完毕，用时{int(used_time // 60)}分{int(used_time % 60)}秒。")
                print(f"共采集到 {len(output_list)} 条原始数据")
                print(f"采集完毕，用时{int(used_time // 60)}分{int(used_time % 60)}秒。", file=file)
                print(f"共采集到 {len(output_list)} 条原始数据", file=file)
        
        # 创建最终输出DataFrame
        if output_list:
            output_df = pd.DataFrame(data=output_list, columns=['企业id', '采集来源', '企业名', '标题', '摘要', '标题链接', '来源', '发布时间'])
            # 最终去重（防止不同企业间的重复）
            output_df = output_df.drop_duplicates(subset=['企业名', '摘要'])
            output_df = output_df.drop_duplicates(subset=['企业名', '标题'])

            # 根据配置决定是否保存到Excel
            if self.save_to_excel:
                output_df.to_excel(self.output_file, index=False)
                print(f"最终结果已保存到 {self.output_file}")
            else:
                print("跳过保存到Excel文件")

            return output_df
        else:
            return pd.DataFrame()
    

    def save_data(self, df):
        """将采集结果保存到MongoDB数据库。

        Args:
            df (DataFrame): 包含采集结果的DataFrame
        """

        with open(self.log_file, 'a+', encoding='utf-8') as log_file:
            print(f'正在将数据写入数据库...')
            print(f'正在将数据写入数据库...', file=log_file)

            try:
                # 清洗数据
                df['标题'], df['摘要'], df['发布时间'] = zip(*df.apply(
                    lambda x: DataUtils.clean_result(x['企业名'], x['标题'], x['摘要'], x['发布时间']), axis=1))
            except:
                print(f'数据清洗失败', file=log_file)
                return

            if self.keep_notes:
                unnamed_columns = {}
                for col in df.columns:
                    if 'Unnamed' in col:
                        unnamed_columns[col] = ''
                df.rename(columns=unnamed_columns, inplace=True)
            else:
                df = df[['企业名', '标题', '摘要', '标题链接', '来源', '发布时间', '采集来源']]

            df = df.sort_values(by=['企业名']).reset_index(drop=True)

            collection1 = monclient(collection="DB_DATA_NEWS_companyb")

            # 将每行保存到MongoDB
            saved_count = 0
            for i in df.index:
                dict1 = {
                    '企业名': df.loc[i, '企业名'],
                    '标题': df.loc[i, '标题'],
                    '摘要': df.loc[i, '摘要'],
                    '标题链接': df.loc[i, '标题链接'],
                    '来源': df.loc[i, '来源'],
                    '发布时间': df.loc[i, '发布时间'],
                    "采集时间": self.collection_time,
                    "是否采集原文": 0,
                    "是否获取标签": 0
                }

                # 根据来源保存到适当的集合
                if df.loc[i, '采集来源'] == '百度资讯':
                    collection1.insert_one(dict1)
                    saved_count += 1

            print(f'数据库保存完成，共保存 {saved_count} 条记录')
            print(f'数据库保存完成，共保存 {saved_count} 条记录', file=log_file)


    async def run(self, limit=None):
        """运行企业新闻采集流程。

        Args:
            limit (int, optional): 限制要处理的公司数量。默认为None。
        """
        # 获取企业列表
        companies = LaungeeAPIUtils.get_enterprise_list()

        # 如果指定了限制，则限制公司数量（优先使用传入的limit参数，其次使用初始化时的limit）
        effective_limit = limit if limit is not None else self.limit
        if effective_limit and isinstance(effective_limit, int) and effective_limit > 0:
            companies = companies[:effective_limit]
        
        print(f"获取到 {len(companies)} 家企业信息")
        print(companies)
        
        # 采集新闻
        results_df = await self.crawl_enterprise_news(companies)

        if self.save_to_mongodb:        
            self.save_data(results_df)
        
        print(f"采集完成，共采集到 {len(results_df)} 条新闻")
        
        return results_df


def main(use_proxy=True, keep_days=14, keep_notes=True, addition_times=1, concurrent_limit=5, limit=10, output_file=None, save_to_excel=True, save_to_mongodb=True):
    """脚本的主入口点。

    Args:
        use_proxy (bool, optional): 是否使用阿布云代理池。默认为True。
        keep_days (int, optional): 保留天数，默认为14天。
        keep_notes (bool, optional): 是否保留备注，默认为True。
        addition_times (int, optional): 补采次数，默认为1次。
        concurrent_limit (int, optional): 并发限制，默认为5。
        limit (int, optional): 限制要处理的公司数量。默认为10。
        output_file (str, optional): 输出文件路径，默认为None（自动生成）。
        save_to_excel (bool, optional): 是否保存到Excel文件，默认为True。
        save_to_mongodb (bool, optional): 是否保存到MongoDB数据库，默认为True。
    """
    collector = EnterpriseNewsCollector(
        use_proxy=use_proxy,
        keep_days=keep_days,
        keep_notes=keep_notes,
        addition_times=addition_times,
        concurrent_limit=concurrent_limit,
        limit=limit,
        output_file=output_file,
        save_to_excel=save_to_excel,
        save_to_mongodb=save_to_mongodb
    )
    
    results = asyncio.run(collector.run(limit=limit))
    
    print(f"采集完成，总用时: {int((time.perf_counter() - collector.start_time) // 60)}分钟")
    print(f"共采集到 {len(results)} 条新闻")


if __name__ == "__main__":
    import argparse
    import warnings
    import logging

    # 忽略 asyncio 相关警告
    warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")

    # 禁用 asyncio 的调试日志
    logging.getLogger("asyncio").setLevel(logging.ERROR)

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='企业新闻采集器')
    parser.add_argument('--no-proxy', action='store_true', help='不使用阿布云代理池')
    parser.add_argument('--keep-days', type=int, default=14, help='保留天数，默认为14天')
    parser.add_argument('--no-keep-notes', action='store_true', help='不保留备注')
    parser.add_argument('--addition-times', type=int, default=1, help='补采次数，默认为1次')
    parser.add_argument('--concurrent-limit', type=int, default=5, help='并发限制，默认为5')
    parser.add_argument('--limit', type=int, default=5, help='限制要处理的公司数量，默认为5')
    parser.add_argument('--output-file', type=str, help='输出文件路径（可以是绝对路径或相对于项目根目录的相对路径）')
    parser.add_argument('--no-save-excel', action='store_true', help='不保存到Excel文件')
    parser.add_argument('--no-save-mongodb', action='store_true', help='不保存到MongoDB数据库')
    args = parser.parse_args()

    # 调用main函数，传递参数
    main(
        use_proxy=not args.no_proxy,
        keep_days=args.keep_days,
        keep_notes=not args.no_keep_notes,
        addition_times=args.addition_times,
        concurrent_limit=args.concurrent_limit,
        limit=args.limit,
        output_file=args.output_file,
        save_to_excel=not args.no_save_excel,
        save_to_mongodb=not args.no_save_mongodb
    )



