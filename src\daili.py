import json
import time
from datetime import datetime, timedelta
import random
# import ddddocr
import ntplib
import requests

from utils.browser_utils import Login<PERSON>rowser, GetSession


class ProxyOperator():
    """
    阿布云代理操作类，用于管理代理服务的登录、续费和状态检查
    创建类时可指定用户名与密码，若不指定则使用默认的用户名与密码
    """
    def __init__(self, User=None, Pass=None):
        """
        初始化代理操作类
        
        Args:
            User (str, optional): 阿布云代理用户名，默认为None时使用内置默认账号
            Pass (str, optional): 阿布云代理密码，默认为None时使用内置默认密码
        """
        self.abuyun_headers = {
            "Referer": "https://center.abuyun.com",
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
        }
        self.time_difference = None
        self.proxy_pool_expire_time = None  # 表示代理的到期时间

        User_tunnelId = {
            'HJ349K4EU1Y2RQ8D': 97085,
            'H7FU74053136906D': 95840,
            'H8R4347UP2710CSD': 95827,
            'HT99W04OL07C735D': 94298,
            # 'H1CV7TI3XBI259DP': 95526,
        }

        if User == None:
            proxyUser = "H7FU74053136906D"
        else:
            proxyUser = User
        if Pass == None:
            proxyPass = "CE305257D84B2C57"
        else:
            proxyPass = Pass

        if User == 'H1CV7TI3XBI259DP':
            proxyHost = "http-pro.abuyun.com"  # 专业版
            proxyPort = "9010"
        else:
            proxyHost = "http-dyn.abuyun.com"  # 动态版
            proxyPort = "9020"

        self.tunnelId = User_tunnelId.get(proxyUser)
        proxyMeta = "http://%(user)s:%(pass)s@%(host)s:%(port)s" % {
            "host": proxyHost,
            "port": proxyPort,
            "user": proxyUser,
            "pass": proxyPass,
        }
        self.proxy = {
            'http' : proxyMeta,
            'https': proxyMeta
        }  #代理
        self.first_login() # 创建类时会进行首次登录的操作，登录阿布云、检查余额、检查代理过期时间、自动续费一小时

        self.allow = True


    def first_login(self):
        """
        登录代理网站，查看代理的到期时间，若已过期或即将过期，则自动续费并启动代理
        
        Returns:
            None
        """
        session, wallet, expire = self.login_proxy_account()
        if wallet < 1:
            print('钱包余额不足，无法续费，请尽快充值。')
            self.logout_proxy_account(session)
            return
        elif wallet < 10:
            print('钱包余额低，请尽快充值以确保正常采集。')
        now = self.get_ntp_time()
        if now >= expire:
            message = '代理池已到期，'
        else:
            message = '代理池已正常启动，'
        message += '到期时间：' + datetime.strftime(expire, '%Y-%m-%d %H:%M:%S') + '，钱包余额：' + str(wallet) + '元，'
        message += '是否开启自动续费（1元/小时）？'
        print(message)

        if expire - timedelta(seconds=300) <= now:
            while True:
                if self.renew_proxy_pool(session):
                    break
                else:
                    print('续费失败，检查续费失败原因')

        self.logout_proxy_account(session)


    def login_proxy_account(self):
        """
        登录阿布云账号，获取钱包余额和代理过期时间
        
        Returns:
            session: 已登录的会话对象
            wallet: 账户钱包余额
            expire: 代理过期时间，datetime对象
        """
        # # 登录阿布云账号
        # session = requests.session()
        #
        # # 登录阿布云
        # print('正在尝试登录阿布云...')
        # ocr = ddddocr.DdddOcr()
        # while True:
        #     login_code = session.get(url='https://center.abuyun.com/captcha', headers=self.abuyun_headers,
        #                              verify=False)  # 获取登录验证码
        #     code = ocr.classification(login_code.content)  # 识别验证码
        #     login_data = {"token": 'laungee', "pass": 'laungee123456', "captcha": code}
        #     responseRes = session.post('https://center.abuyun.com/backend/passport/account/auth/verify',
        #                                data=json.dumps(login_data), headers=self.abuyun_headers, verify=False)
        #     print(login_data)
        #     print(responseRes.text)
        #     if json.loads(responseRes.text.split('\n')[-1])['code'] == 0:
        #         print('登陆成功。')
        #         break
        #     else:
        #         print('验证码错误，正在重试...')
        #         time.sleep(3)
        browser = LoginBrowser(ip='127.0.0.1', port=9222)
        session = GetSession()
        abu_url = 'https://center.abuyun.com/#/dashboard'
        abu_website = browser.openBrowserWebsite(abu_url)
        try:
            print("刷新一次页面")
            abu_website.refresh()  # 刷新页面
            time.sleep(3)  # 等待3s
        except Exception as e1:
            print("刷新页面报错\n报错信息:{}".format(e1))
        session = session.get(abu_website)
        # 获取钱包余额center.abuyun.com
        wallet = float(json.loads(
            session.get(url='https://center.abuyun.com/backend/passport/wallet/profile/details',
                        headers=self.abuyun_headers,
                        verify=False).text.split('\n')[-1])['result']['wallet']['FreeBalance'])

        # wallet = session.get(url='https://center.abuyun.com/backend/passport/wallet/profile/details',
        #                 headers=self.abuyun_headers,
        #                 verify=False).text
        # wallet = 10
        # reswallet = session.get(url='https://center.abuyun.com/backend/passport/wallet/profile/details',headers=self.abuyun_headers,
        #             verify=False)

        print("当前余额为：{}元".format(wallet))


        # 获取代理池过期时间
        tunnels = json.loads(
            session.get(url='https://center.abuyun.com/backend/cloud/http/tunnel/lists?capacity=20&level=3&p=1',
                        headers=self.abuyun_headers, verify=False).text.split('\n')[-1])['result']['lists']
        for tunnel in tunnels:
            if tunnel['TunnelId'] == self.tunnelId:
                expire = datetime.strptime(tunnel['ExpireTime'], '%Y-%m-%d %H:%M:%S')

        self.proxy_pool_expire_time = expire

        return session, wallet, expire

    def logout_proxy_account(self, session):
        """
        退出阿布云账号
        
        Args:
            session: 已登录的会话对象
            
        Returns:
            None
        """
        # 退出阿布云账号
        for i in range(5):
            if session.post('https://center.abuyun.com/backend/passport/account/auth/logout', headers=self.abuyun_headers,
                            verify=False).status_code == 200:
                session.close()
                break
            else:
                time.sleep(10)

    def renew_proxy_pool(self, session):
        """
        续费一小时代理隧道
        
        Args:
            session: 已登录的会话对象
            
        Returns:
            bool: 续费成功返回True，失败返回False
        """
        # 续费一小时代理隧道
        charge_data = [
            {"ProductId": 14, "Tunnels": [str(self.tunnelId)], "Periods": 1, "Requests": 0}]
        charge = session.post('https://center.abuyun.com/backend/cloud/http/tunnel/charge',
                              data=json.dumps(charge_data),
                              headers=self.abuyun_headers, verify=False)  # 下订单
        if charge.status_code == 200:
            trade_no = json.loads(charge.text.split('\n')[-1])["result"]["order"]["TradeNo"]
            pay_data = {"TradeNo": trade_no, "PayMode": 2}
            if session.post('https://center.abuyun.com/backend/trade/order/profile/payForOrder', json.dumps(pay_data),
                            headers=self.abuyun_headers, verify=False).status_code == 200:  # 付费
                tunnels = json.loads(
                    session.get(url='https://center.abuyun.com/backend/cloud/http/tunnel/lists?capacity=20&level=3&p=1',
                                headers=self.abuyun_headers, verify=False).text.split('\n')[-1])['result']['lists']
                for tunnel in tunnels:
                    if tunnel['TunnelId'] == self.tunnelId:
                        expire = datetime.strptime(tunnel['ExpireTime'], '%Y-%m-%d %H:%M:%S')
                self.proxy_pool_expire_time = expire  # 更新过期时间
                wallet = float(json.loads(
                    session.get(url='https://center.abuyun.com/backend/passport/wallet/profile/details',
                                headers=self.abuyun_headers, verify=False).text.split('\n')[-1])['result']['wallet'][
                                   'FreeBalance'])
                print('续费成功，钱包余额：' + str(wallet) + '元，到期时间：' + datetime.strftime(expire,
                                                                                              '%Y-%m-%d %H:%M:%S'))
            else:
                return False
        else:
            return False
        return True



    def check_proxy_pool(self):
        """
        检查代理隧道是否过期，如果即将过期则自动续费
        
        Returns:
            None
        """
        # 检查代理隧道是否过期
        now = self.get_ntp_time()
        if self.proxy_pool_expire_time - timedelta(seconds=300) <= now:  # 即将过期
            print('即将过期, 过期时间：{}'.format(self.proxy_pool_expire_time))
            session, wallet, expire = self.login_proxy_account()
            if expire - timedelta(seconds=300) <= now:  # 登录确认过期时间
                if wallet < 1:
                    raise ('余额不足，续费失败，请尽快充值。')
                else:
                    print('开始续费')
                    # 续费一小时代理
                    while True:
                        if self.renew_proxy_pool(session):
                            print('续费一小时代理,过期时间：{}'.format(self.proxy_pool_expire_time))
                            self.logout_proxy_account(session)
                            break
                        else:
                            print('续费失败，检查续费失败原因')
            else:
                print('获取时间后发现未过期, 过期时间：{}'.format(self.proxy_pool_expire_time))
                time.sleep(10)
                self.logout_proxy_account(session)
        else:
            pass


    def get_ntp_time(self):
        """
        获取网络时间，通过NTP服务器同步时间
        
        Returns:
            datetime: 当前网络时间的datetime对象
        """
        # 获取网络时间
        if self.time_difference:
            now = datetime.now() + self.time_difference
        else:
            ntp_server = ['ntp.aliyun.com', 'time1.cloud.tencent.com', 'time2.cloud.tencent.com',
                          'time3.cloud.tencent.com', 'time4.cloud.tencent.com', 'time5.cloud.tencent.com',
                          'ntp.ntsc.ac.cn', 'cn.ntp.org.cn']
            c = ntplib.NTPClient()
            while True:
                try:
                    response = c.request(random.choice(ntp_server), timeout=0.5)
                    ts = response.tx_time
                    str_time = time.strftime('%Y-%m-%d %X', time.localtime(ts))
                    now = datetime.strptime(str_time, '%Y-%m-%d %H:%M:%S')
                    break
                except:
                    pass
            # 计算时间偏差
            self.time_difference = now - datetime.now()
        return now
    
    
if __name__ == "__main__":
    proxy=ProxyOperator()
    proxy.logout_proxy_account()

