# -*- coding: utf-8 -*-
"""
API测试脚本

用于测试Flask API的各个端点功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:5000"

def test_main_endpoints():
    """测试主要端点"""
    print("=== 测试主要端点 ===")
    
    endpoints = [
        "/",
        "/health", 
        "/api/info",
        "/api/docs"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"GET {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"  ✓ 成功")
            else:
                print(f"  ✗ 失败")
        except Exception as e:
            print(f"GET {endpoint}: 连接错误 - {e}")

def test_enterprise_endpoints():
    """测试企业提取器端点"""
    print("\n=== 测试企业提取器端点 ===")
    
    # 测试功能测试端点
    try:
        response = requests.get(f"{BASE_URL}/enterprise/test")
        print(f"GET /enterprise/test: {response.status_code}")
        if response.status_code == 200:
            print(f"  ✓ 企业提取器功能正常")
        else:
            print(f"  ✗ 企业提取器功能异常")
    except Exception as e:
        print(f"GET /enterprise/test: 连接错误 - {e}")
    
    # 测试搜索端点（GET方式）
    test_url = "https://www.baidu.com/s?tn=news&word=腾讯"
    try:
        response = requests.get(f"{BASE_URL}/enterprise/search", params={
            "url": test_url,
            "max_retries": 1
        })
        print(f"GET /enterprise/search: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✓ 搜索成功，结果数量: {data.get('total_count', 0)}")
        else:
            print(f"  ✗ 搜索失败")
    except Exception as e:
        print(f"GET /enterprise/search: 连接错误 - {e}")

def test_wechat_endpoints():
    """测试微信提取器端点"""
    print("\n=== 测试微信提取器端点 ===")
    
    # 测试功能测试端点
    try:
        response = requests.get(f"{BASE_URL}/wx/test")
        print(f"GET /wx/test: {response.status_code}")
        if response.status_code == 200:
            print(f"  ✓ 微信提取器功能正常")
        else:
            print(f"  ✗ 微信提取器功能异常")
    except Exception as e:
        print(f"GET /wx/test: 连接错误 - {e}")
    
    # 测试微信内容提取（使用示例URL）
    test_url = "https://mp.weixin.qq.com/s/example123"
    try:
        response = requests.get(f"{BASE_URL}/wx/extract", params={
            "url": test_url,
            "max_retries": 1
        })
        print(f"GET /wx/extract: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✓ 提取成功，结果数量: {data.get('total_count', 0)}")
        elif response.status_code == 400:
            print(f"  ⚠ 参数验证正常（预期的400错误）")
        else:
            print(f"  ✗ 提取失败")
    except Exception as e:
        print(f"GET /wx/extract: 连接错误 - {e}")

def test_batch_endpoints():
    """测试批量处理端点"""
    print("\n=== 测试批量处理端点 ===")
    
    # 测试企业批量搜索
    try:
        response = requests.post(f"{BASE_URL}/enterprise/batch-search", json={
            "urls": [
                "https://www.baidu.com/s?tn=news&word=腾讯",
                "https://www.baidu.com/s?tn=news&word=阿里巴巴"
            ],
            "max_retries": 1,
            "concurrent_limit": 1
        })
        print(f"POST /enterprise/batch-search: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✓ 批量搜索成功，处理数量: {data.get('total_processed', 0)}")
        else:
            print(f"  ✗ 批量搜索失败")
    except Exception as e:
        print(f"POST /enterprise/batch-search: 连接错误 - {e}")
    
    # 测试微信批量提取
    try:
        response = requests.post(f"{BASE_URL}/wx/batch-extract", json={
            "urls": [
                "https://mp.weixin.qq.com/s/example1",
                "https://mp.weixin.qq.com/s/example2"
            ],
            "max_retries": 1,
            "concurrent_limit": 1
        })
        print(f"POST /wx/batch-extract: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✓ 批量提取成功，处理数量: {data.get('total_processed', 0)}")
        else:
            print(f"  ✗ 批量提取失败")
    except Exception as e:
        print(f"POST /wx/batch-extract: 连接错误 - {e}")

def main():
    """主测试函数"""
    print("开始API功能测试...")
    print(f"测试目标: {BASE_URL}")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 执行各项测试
    test_main_endpoints()
    test_enterprise_endpoints()
    test_wechat_endpoints()
    test_batch_endpoints()
    
    print("\n" + "=" * 50)
    print("API功能测试完成！")
    print("\n注意：")
    print("- 某些测试可能因为网络或服务器配置而失败")
    print("- 实际的URL提取功能需要有效的网络连接")
    print("- 批量处理测试使用了较小的重试次数以加快测试速度")

if __name__ == "__main__":
    main()
