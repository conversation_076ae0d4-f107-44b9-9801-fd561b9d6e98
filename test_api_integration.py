# -*- coding: utf-8 -*-
"""
API集成测试脚本

测试app/routes中的API是否能正确调用src/中的现有函数
"""

import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_web_content_extractor():
    """测试WebContentExtractor的模块化函数"""
    print("=== 测试WebContentExtractor ===")
    
    try:
        from src.web_content_extractor import WebContentExtractor
        
        # 创建临时实例
        temp_filename = "test_temp.xlsx"
        extractor = WebContentExtractor(
            output_filename=temp_filename,
            use_proxy=False,
            batch_size=1,
            enable_dify_analysis=False,
            thread_count=1,
            save_excel=False,
            save_mongodb=False
        )
        
        print("✓ WebContentExtractor实例创建成功")
        
        # 测试extract_single_url方法
        test_url = "https://www.baidu.com"
        print(f"测试URL: {test_url}")
        
        result = extractor.extract_single_url(test_url, max_retries=1)
        print(f"提取结果: success={result['success']}")
        if result['success']:
            print(f"  内容长度: {len(result['content'])}")
            print(f"  实际URL: {result['actual_url']}")
        else:
            print(f"  错误: {result['error']}")
        
        # 关闭资源
        extractor.close()
        print("✓ WebContentExtractor测试完成")
        
    except Exception as e:
        print(f"✗ WebContentExtractor测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_wechat_extractor():
    """测试WechatArticleExtractor的模块化函数"""
    print("\n=== 测试WechatArticleExtractor ===")
    
    try:
        from src.wx_content_extractor import WechatArticleExtractor
        
        # 创建实例
        extractor = WechatArticleExtractor(
            use_proxy=False,
            thread_count=1
        )
        
        print("✓ WechatArticleExtractor实例创建成功")
        
        # 测试URL验证
        test_urls = [
            "https://mp.weixin.qq.com/s/test123",
            "https://www.baidu.com"
        ]
        
        for url in test_urls:
            is_wechat = extractor.is_wechat_url(url)
            print(f"URL验证: {url} -> {is_wechat}")
        
        # 测试extract_single_url方法（使用示例URL，可能会失败但能测试方法调用）
        test_url = "https://mp.weixin.qq.com/s/example"
        print(f"测试微信URL: {test_url}")
        
        result = extractor.extract_single_url(test_url, max_retries=1, generate_pdf=False)
        print(f"提取结果: success={result['success']}")
        if not result['success']:
            print(f"  错误（预期）: {result['error']}")
        
        # 关闭资源
        extractor.close()
        print("✓ WechatArticleExtractor测试完成")
        
    except Exception as e:
        print(f"✗ WechatArticleExtractor测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_service_layer():
    """测试service层是否能正确调用src中的函数"""
    print("\n=== 测试Service层 ===")
    
    try:
        from app.service.search import EnterpriseNewsSearcher, WeChatContentSearcher
        
        # 测试企业新闻搜索器
        print("测试EnterpriseNewsSearcher...")
        enterprise_searcher = EnterpriseNewsSearcher(use_proxy=False)
        print("✓ EnterpriseNewsSearcher初始化成功")
        
        # 测试微信内容搜索器
        print("测试WeChatContentSearcher...")
        wechat_searcher = WeChatContentSearcher(use_proxy=False)
        print("✓ WeChatContentSearcher初始化成功")
        
        # 测试URL验证
        test_url = "https://mp.weixin.qq.com/s/test"
        is_wechat = wechat_searcher.is_wechat_url(test_url)
        print(f"微信URL验证: {test_url} -> {is_wechat}")
        
        # 关闭资源
        wechat_searcher.close()
        print("✓ Service层测试完成")
        
    except Exception as e:
        print(f"✗ Service层测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_import_paths():
    """测试导入路径是否正确"""
    print("\n=== 测试导入路径 ===")
    
    # 测试src模块导入
    try:
        from src.web_content_extractor import WebContentExtractor
        print("✓ src.web_content_extractor导入成功")
    except ImportError as e:
        print(f"✗ src.web_content_extractor导入失败: {e}")
    
    try:
        from src.wx_content_extractor import WechatArticleExtractor
        print("✓ src.wx_content_extractor导入成功")
    except ImportError as e:
        print(f"✗ src.wx_content_extractor导入失败: {e}")
    
    # 测试utils模块导入
    try:
        from src.utils.time_utils import TimeUtils
        print("✓ src.utils.time_utils导入成功")
    except ImportError as e:
        print(f"✗ src.utils.time_utils导入失败: {e}")
    
    # 测试app模块导入
    try:
        from app.service.search import EnterpriseNewsSearcher
        print("✓ app.service.search导入成功")
    except ImportError as e:
        print(f"✗ app.service.search导入失败: {e}")

def main():
    """主测试函数"""
    print("开始API集成测试...")
    print("=" * 50)
    
    # 测试导入路径
    test_import_paths()
    
    # 测试src中的现有函数
    test_web_content_extractor()
    test_wechat_extractor()
    
    # 测试service层
    test_service_layer()
    
    print("\n" + "=" * 50)
    print("API集成测试完成！")
    print("\n说明：")
    print("- 此测试验证了app/routes中的API能否正确调用src/中的现有函数")
    print("- 某些测试可能因为网络或配置问题而失败，这是正常的")
    print("- 重点是验证模块导入和方法调用是否正常")

if __name__ == "__main__":
    main()
