# -*- coding: utf-8 -*-
"""
企业提取器路由模块

本模块提供企业相关的搜索和提取功能，包括：
- 企业新闻搜索
- 企业信息提取
- 批量企业搜索
- 企业数据管理

主要路由：
- /enterprise/search - 企业搜索接口
- /enterprise/news - 企业新闻搜索
- /enterprise/batch-search - 批量企业搜索
- /enterprise/info - 企业信息获取
"""

import sys
import os
import asyncio
import json
import time
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from typing import List, Dict, Any, Optional
from urllib.parse import quote

# 添加src/utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'utils'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'service'))

# 导入搜索器和工具类
from service.search import EnterpriseNewsSearcher, Searcher, WeChatContentSearcher

# 创建企业提取器路由蓝图
enterprise_extractor_bp = Blueprint('enterprise_extractor', __name__, url_prefix='/enterprise')


@enterprise_extractor_bp.route('/search', methods=['GET', 'POST'])
def enterprise_search():
    """
    企业搜索接口

    支持GET和POST请求，用于搜索企业相关信息

    Args:
        url (str): 搜索URL（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）

    Returns:
        JSON: 包含搜索结果的响应
            - success (bool): 请求是否成功
            - results (list): 搜索结果列表
            - error (str): 错误信息（仅在失败时返回）
    """
    try:
        # 获取请求参数
        if request.method == 'GET':
            url = request.args.get('url')
            use_proxy = request.args.get('use_proxy', 'false').lower() == 'true'
            max_retries = int(request.args.get('max_retries', 3))
        else:  # POST
            data = request.get_json() or {}
            url = data.get('url')
            use_proxy = data.get('use_proxy', False)
            max_retries = data.get('max_retries', 3)

        # 参数验证
        if not url:
            return jsonify({
                "success": False,
                "error": "缺少必填参数：url"
            }), 400

        # 创建搜索器实例
        searcher = EnterpriseNewsSearcher(use_proxy=use_proxy)

        # 执行异步搜索
        results = asyncio.run(searcher.async_search(url, max_retries=max_retries))

        return jsonify({
            "success": True,
            "results": results,
            "total_count": len(results) if results else 0,
            "timestamp": datetime.now().isoformat()
        })

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": f"参数错误：{str(e)}"
        }), 400
    except Exception as e:
        # 记录详细错误信息
        error_details = traceback.format_exc()
        print(f"企业搜索接口错误：{error_details}")

        return jsonify({
            "success": False,
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@enterprise_extractor_bp.route('/batch-search', methods=['POST'])
def batch_enterprise_search():
    """
    批量企业搜索接口

    支持批量处理多个URL的企业搜索请求

    Args:
        urls (list): URL列表（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）
        concurrent_limit (int): 并发限制（可选，默认3）

    Returns:
        JSON: 包含批量搜索结果的响应
            - success (bool): 请求是否成功
            - results (list): 搜索结果列表
            - total_processed (int): 处理的URL总数
            - successful_count (int): 成功处理的URL数量
            - failed_count (int): 失败的URL数量
    """
    try:
        data = request.get_json() or {}
        urls = data.get('urls', [])
        use_proxy = data.get('use_proxy', False)
        max_retries = data.get('max_retries', 3)
        concurrent_limit = data.get('concurrent_limit', 3)

        # 参数验证
        if not urls or not isinstance(urls, list):
            return jsonify({
                "success": False,
                "error": "缺少必填参数：urls（必须是列表格式）"
            }), 400

        if len(urls) > 100:  # 限制批量处理数量
            return jsonify({
                "success": False,
                "error": "批量处理URL数量不能超过100个"
            }), 400

        # 创建搜索器实例
        searcher = EnterpriseNewsSearcher(use_proxy=use_proxy)

        # 批量处理结果
        all_results = []
        successful_count = 0
        failed_count = 0

        # 使用信号量控制并发
        async def process_urls():
            semaphore = asyncio.Semaphore(concurrent_limit)

            async def process_single_url(url):
                nonlocal successful_count, failed_count
                async with semaphore:
                    try:
                        result = await searcher.async_search(url, max_retries=max_retries)
                        if result:
                            successful_count += 1
                            return {"url": url, "success": True, "data": result}
                        else:
                            failed_count += 1
                            return {"url": url, "success": False, "error": "未获取到数据"}
                    except Exception as e:
                        failed_count += 1
                        return {"url": url, "success": False, "error": str(e)}

            tasks = [process_single_url(url) for url in urls]
            return await asyncio.gather(*tasks)

        # 执行批量搜索
        batch_results = asyncio.run(process_urls())

        return jsonify({
            "success": True,
            "results": batch_results,
            "total_processed": len(urls),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        error_details = traceback.format_exc()
        print(f"批量企业搜索接口错误：{error_details}")

        return jsonify({
            "success": False,
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@enterprise_extractor_bp.route('/test', methods=['GET'])
def test_enterprise_extractor():
    """
    企业提取器功能测试接口

    用于测试企业提取器的基本功能是否正常

    Returns:
        JSON: 测试结果响应
    """
    try:
        # 测试搜索器初始化
        searcher = EnterpriseNewsSearcher()

        return jsonify({
            "success": True,
            "message": "企业提取器功能正常",
            "test_results": {
                "searcher_initialized": True,
                "available_methods": [
                    "async_search",
                    "build_search_url",
                    "extract_search_results"
                ]
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"测试失败：{str(e)}"
        }), 500


# 错误处理器
@enterprise_extractor_bp.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "error": "接口不存在",
        "message": "请检查URL路径是否正确",
        "available_endpoints": [
            "/enterprise/search",
            "/enterprise/batch-search",
            "/enterprise/test"
        ]
    }), 404


@enterprise_extractor_bp.errorhandler(405)
def method_not_allowed_error(error):
    """405错误处理"""
    return jsonify({
        "success": False,
        "error": "请求方法不被允许",
        "message": "请检查HTTP方法是否正确"
    }), 405


@enterprise_extractor_bp.errorhandler(500)
def internal_server_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "error": "服务器内部错误",
        "message": "请稍后重试或联系管理员"
    }), 500