import re
import textwrap
import os
import sys
import base64
import hashlib
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

from bs4 import BeautifulSoup

import pandas as pd
import time
from gne import GeneralNewsExtractor
from selenium.webdriver.common.by import By

from datetime import datetime
import traceback

from utils.MongoClient import monclient
from utils.dify import DifyEnterpriseAnalyzer
from utils.browser_utils import LoginBrowser, IndependentBrowser


# 将项目根目录添加到路径
root_path = os.path.abspath(os.path.join(os.path.abspath(__file__), '..', '..'))
sys.path.append(root_path)

class WebContentExtractor:
    """
    网页内容提取器，使用Chrome浏览器
    """
    def __init__(self, output_filename, use_proxy=False, batch_size=10, mongo_collection="web_content_data",
                 dify_api_url="http://localhost/v1", dify_api_key="app-your-api-key-here", enable_dify_analysis=True,
                 dify_max_retries=3, dify_retry_delay=2, thread_count=4, save_excel=True, save_mongodb=True):
        """
        初始化提取器

        Args:
            output_filename (str): 输出文件名
            use_proxy (bool): 是否使用代理，默认为False
            batch_size (int): 批量保存到MongoDB的数据条数，默认为10
            mongo_collection (str): MongoDB集合名称，默认为"web_content_data"
            dify_api_url (str): Dify API 基础URL，默认为本地实例
            dify_api_key (str): Dify API 密钥
            enable_dify_analysis (bool): 是否启用Dify文本分析，默认为True
            dify_max_retries (int): Dify分析最大重试次数，默认为3
            dify_retry_delay (int): Dify分析重试间隔秒数，默认为2
            thread_count (int): 线程数量，默认为4
            save_excel (bool): 是否保存到Excel文件，默认为True
            save_mongodb (bool): 是否保存到MongoDB数据库，默认为True
        """
        self.use_proxy = use_proxy
        self.batch_size = batch_size
        self.mongo_collection = mongo_collection
        self.enable_dify_analysis = enable_dify_analysis
        self.dify_max_retries = dify_max_retries
        self.dify_retry_delay = dify_retry_delay
        self.thread_count = thread_count
        self.save_excel = save_excel
        self.save_mongodb = save_mongodb

        # 创建日志目录
        self.log_filename = f"网页内容爬取日志_{datetime.now().strftime('%Y-%m-%d %H-%M-%S')}.log"
        self.log_dir = os.path.join(root_path, "log", "网页内容爬取日志")
        os.makedirs(self.log_dir, exist_ok=True)
        self.log_file = os.path.join(self.log_dir, self.log_filename)
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write(f"爬虫启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # 创建excel输出目录和文件路径
        if os.path.isabs(output_filename):
            # 如果是绝对路径，直接使用
            self.output_file = output_filename
            self.output_dir = os.path.dirname(output_filename)
            self.output_filename = os.path.basename(output_filename)
        else:
            # 如果是相对路径，相对于项目根目录的output文件夹
            self.output_filename = output_filename
            self.output_dir = os.path.join(root_path, "output")
            self.output_file = os.path.join(self.output_dir, self.output_filename)

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 创建PDF输出目录
        self.pdf_output_dir = os.path.join(self.output_dir, "pdf")
        os.makedirs(self.pdf_output_dir, exist_ok=True)

        # 初始化MongoDB客户端
        try:
            self.mongo_client = monclient(collection=mongo_collection)
            self.log(f"MongoDB连接成功，数据库: {self.mongo_client.db.name}, 集合: {mongo_collection}")
            # 测试连接
            collections = self.mongo_client.get_all_col()
            self.log(f"可用集合: {collections}")
            if mongo_collection in collections:
                self.log(f"目标集合 '{mongo_collection}' 存在")
            else:
                self.log(f"警告: 目标集合 '{mongo_collection}' 不存在，将使用默认集合")
        except Exception as e:
            self.log(f"MongoDB连接失败: {str(e)}")
            self.mongo_client = None

        # 初始化Dify文本分析器
        if self.enable_dify_analysis:
            try:
                self.dify_analyzer = DifyEnterpriseAnalyzer(dify_api_url, dify_api_key)
            except Exception as e:
                self.dify_analyzer = None
                self.enable_dify_analysis = False
        else:
            self.dify_analyzer = None

        # TODO: selenium 代理功能的实现；

        # 初始化浏览器驱动池，使用动态分配机制
        self.drivers = []
        self.driver_lock = threading.Lock()  # 用于线程安全的驱动分配
        self.driver_busy = []  # 跟踪每个驱动的忙碌状态
        self.driver_condition = threading.Condition(self.driver_lock)  # 用于等待空闲驱动

        # 创建独立的浏览器驱动实例池
        successful_drivers = 0
        for i in range(self.thread_count):
            try:
                # 每个线程使用完全独立的Chrome实例（工具函数已包含防检测设置）
                driver = IndependentBrowser.create_independent_browser(
                    headless=False,  # 不使用无头模式，便于调试
                )

                self.drivers.append(driver)
                self.driver_busy.append(False)  # 初始状态为空闲
                successful_drivers += 1
                self.log(f"初始化独立浏览器驱动 {i+1}/{self.thread_count}")

            except Exception as e:
                self.log(f"初始化浏览器驱动 {i+1} 失败: {str(e)}")
                continue

        if successful_drivers == 0:
            raise Exception("所有浏览器驱动初始化失败")

        # 调整线程数量为实际成功的驱动数量
        if successful_drivers < self.thread_count:
            self.log(f"警告: 只成功初始化了 {successful_drivers} 个驱动，线程数量调整为 {successful_drivers}")
            self.thread_count = successful_drivers

        self.log(f"成功初始化 {successful_drivers} 个独立浏览器驱动")

        # 初始化GNE正文提取器（每个驱动一个）
        self.extractors = [GeneralNewsExtractor() for _ in range(len(self.drivers))]

        # 线程本地存储，用于在多线程环境中存储每个线程的浏览器实例
        self.thread_local = threading.local()
        

    def process_dataframe(self, df):
        """
        使用多线程处理DataFrame，提取'标题链接'列中的网页内容并生成PDF文件

        Args:
            df (pandas.DataFrame): 包含'标题链接'列的DataFrame

        Returns:
            pandas.DataFrame: 添加了'网页内容'、'网页pdf'(文件名)、'实际访问URL'和Dify分析结果列的DataFrame
        """
        if '标题链接' not in df.columns:
            self.log("错误: DataFrame中没有'标题链接'列")
            return df

        # 添加新列
        df['网页内容'] = ""
        df['网页pdf'] = ""
        df['实际访问URL'] = ""

        # 添加Dify分析结果列
        if self.enable_dify_analysis:
            df['内容标签'] = ""
            df['情感极性'] = ""
            df['情感强度'] = 0.0
            df['细粒度分析'] = ""

        total_urls = len(df)
        self.log(f"开始使用 {self.thread_count} 个线程处理 {total_urls} 个URL")

        # 使用线程池处理URL，采用动态驱动分配机制
        with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
            # 提交所有任务，不再需要预分配线程索引
            futures = []

            for index, row in df.iterrows():
                future = executor.submit(self.process_single_url, index, row, total_urls)
                futures.append((future, index))

            # 处理完成的任务
            processed_count = 0
            completed_results = []

            for future, original_index in futures:
                try:
                    result = future.result()
                    index = result['index']

                    # 更新DataFrame
                    df.at[index, '网页内容'] = result['网页内容']
                    df.at[index, '网页pdf'] = result['网页pdf']
                    df.at[index, '实际访问URL'] = result['实际访问URL']

                    if self.enable_dify_analysis:
                        df.at[index, '内容标签'] = result['内容标签']
                        df.at[index, '情感极性'] = result['情感极性']
                        df.at[index, '情感强度'] = result['情感强度']
                        df.at[index, '细粒度分析'] = result['细粒度分析']

                    processed_count += 1
                    completed_results.append(result)

                    self.log(f"已完成 {processed_count}/{total_urls} 个URL的处理")

                    # 每batch_size条记录保存一次到MongoDB
                    if self.save_mongodb and len(completed_results) >= self.batch_size:
                        # 获取已完成的结果对应的DataFrame行
                        indices = [r['index'] for r in completed_results]
                        batch_df = df.iloc[indices].copy()

                        # 保存到MongoDB
                        self.save_to_mongodb(batch_df)
                        self.log(f"已保存 {len(batch_df)} 条记录到MongoDB")
                        completed_results.clear()

                    # 每batch_size条记录保存一次Excel，避免数据丢失
                    if self.save_excel and processed_count % self.batch_size == 0:
                        self.save_to_excel(df)
                        self.log(f"已保存中间结果到: {self.output_file}")

                except Exception as e:
                    self.log(f"处理索引 {original_index} 的任务时出错: {str(e)}")
                    traceback.print_exc()

        # 保存剩余的数据到MongoDB
        if self.save_mongodb and completed_results:
            indices = [r['index'] for r in completed_results]
            remaining_df = df.iloc[indices].copy()
            self.save_to_mongodb(remaining_df)
            self.log(f"已保存剩余 {len(remaining_df)} 条记录到MongoDB")

        if self.save_excel:
            self.save_to_excel(df)
            self.log(f"已保存最终结果到: {self.output_file}")
        else:
            self.log("跳过保存到Excel文件")

        self.log(f"多线程处理完成，共 {total_urls} 个URL，使用了 {self.thread_count} 个线程")
        return df

    def extract_single_url(self, url, max_retries=3):
        """
        提取单个URL的内容，返回结构化结果
        这是一个独立的、可被API调用的方法

        Args:
            url (str): 要处理的URL
            max_retries (int): 最大重试次数，默认为3

        Returns:
            dict: 提取结果，包含内容、PDF文件名、实际URL等信息
        """
        result = {
            'success': True,
            'url': url,
            'content': "",
            'pdf_filename': "",
            'actual_url': "",
            'extracted_at': datetime.now().isoformat(),
            'error': ""
        }

        driver_index = -1
        try:
            # 动态获取空闲的驱动和提取器
            driver, extractor, driver_index = self.get_available_driver_and_extractor()

            # 提取网页内容和实际URL
            content, actual_url = self.extract_content(url, driver, extractor, max_retries)
            result['content'] = content
            result['actual_url'] = actual_url

            # 生成PDF并保存到本地文件
            if content:  # 只有成功提取内容才生成PDF
                pdf_filename = self.generate_pdf_and_save(url, actual_url, driver)
                result['pdf_filename'] = pdf_filename

        except Exception as e:
            error_msg = f"提取URL {url} 时出错: {str(e)}"
            self.log(error_msg)
            result['success'] = False
            result['error'] = error_msg
            traceback.print_exc()
        finally:
            # 确保释放驱动
            if driver_index >= 0:
                self.release_driver(driver_index)

        return result

    def analyze_single_content(self, content, enterprise_name="", title=""):
        """
        分析单个内容的情感和标签
        这是一个独立的、可被API调用的方法

        Args:
            content (str): 要分析的内容
            enterprise_name (str): 企业名称
            title (str): 文章标题

        Returns:
            dict: 分析结果
        """
        if not self.enable_dify_analysis or not content:
            return {
                'success': False,
                'content_tags': "",
                'sentiment_polarity': "Neutral",
                'sentiment_intensity': 0.0,
                'aspect_analysis': "",
                'error': "Dify分析未启用或内容为空"
            }

        try:
            analysis_result = self.analyze_content_with_dify(
                content, enterprise_name, title,
                self.dify_max_retries, self.dify_retry_delay
            )

            if analysis_result["success"]:
                tags = analysis_result["classification"]["tags"]
                aspect_analysis = analysis_result["sentiment"].get("aspect_analysis", [])
                aspect_str = '; '.join([f"{item['aspect']}:{item['score']}" for item in aspect_analysis]) if aspect_analysis else ""

                return {
                    'success': True,
                    'content_tags': ', '.join(tags) if tags else "",
                    'sentiment_polarity': analysis_result["sentiment"]["polarity"],
                    'sentiment_intensity': analysis_result["sentiment"]["intensity"],
                    'aspect_analysis': aspect_str,
                    'error': ""
                }
            else:
                return {
                    'success': False,
                    'content_tags': "",
                    'sentiment_polarity': "Neutral",
                    'sentiment_intensity': 0.0,
                    'aspect_analysis': "",
                    'error': analysis_result.get('error', '分析失败')
                }

        except Exception as e:
            return {
                'success': False,
                'content_tags': "",
                'sentiment_polarity': "Neutral",
                'sentiment_intensity': 0.0,
                'aspect_analysis': "",
                'error': f"分析异常: {str(e)}"
            }

    def process_single_url(self, index, row, total_urls):
        """
        处理单个URL的工作函数，用于多线程处理，使用动态驱动分配

        Args:
            index (int): 行索引
            row (pandas.Series): DataFrame行数据
            total_urls (int): 总URL数量

        Returns:
            dict: 处理结果，包含所有需要更新的字段
        """
        url = row['标题链接']
        thread_id = threading.current_thread().ident
        self.log(f"线程 {thread_id} 处理 ({index+1}/{total_urls}): {url}")

        result = {
            'index': index,
            '网页内容': "",
            '网页pdf': "",
            '实际访问URL': "",
            '内容标签': "",
            '情感极性': "Neutral",
            '情感强度': 0.0,
            '细粒度分析': ""
        }

        driver_index = -1
        try:
            # 动态获取空闲的驱动和提取器
            driver, extractor, driver_index = self.get_available_driver_and_extractor()

            # 提取网页内容和实际URL
            content, actual_url = self.extract_content(url, driver, extractor)
            result['网页内容'] = content
            result['实际访问URL'] = actual_url

            # 生成PDF并保存到本地文件（使用原始URL的MD5作为文件名）
            pdf_filename = self.generate_pdf_and_save(url, actual_url, driver)
            result['网页pdf'] = pdf_filename

            # 使用Dify分析内容（如果启用）
            if self.enable_dify_analysis and content:
                title = row.get('标题', '') if '标题' in row else ''
                enterprise = row.get('企业名', '') if '企业名' in row else ''
                analysis_result = self.analyze_content_with_dify(content, enterprise, title,
                                                                self.dify_max_retries, self.dify_retry_delay)

                if analysis_result["success"]:
                    # 保存分析结果
                    tags = analysis_result["classification"]["tags"]
                    result['内容标签'] = ', '.join(tags) if tags else ""
                    result['情感极性'] = analysis_result["sentiment"]["polarity"]
                    result['情感强度'] = analysis_result["sentiment"]["intensity"]

                    # 保存细粒度分析结果
                    aspect_analysis = analysis_result["sentiment"].get("aspect_analysis", [])
                    if aspect_analysis:
                        aspect_str = '; '.join([f"{item['aspect']}:{item['score']}" for item in aspect_analysis])
                        result['细粒度分析'] = aspect_str
                else:
                    self.log(f"URL {url} 的Dify分析失败，使用默认值")

            # 添加延迟，避免请求过于频繁
            time.sleep(0.5)  # 多线程情况下减少延迟

        except Exception as e:
            self.log(f"处理URL {url} 时出错: {str(e)}")
            traceback.print_exc()
        finally:
            # 确保释放驱动
            if driver_index >= 0:
                self.release_driver(driver_index)

        return result

    def extract_content(self, url, driver, extractor, max_retries=2):
        """
        提取网页内容，支持重试和跳过验证码/登录页面

        Args:
            url (str): 网页URL
            driver: 浏览器驱动实例
            extractor: GNE提取器实例
            max_retries (int): 最大重试次数，默认为2

        Returns:
            tuple: (提取的文章内容, 实际访问的URL)
        """
        for attempt in range(max_retries + 1):
            try:
                self.log(f"尝试访问URL (第{attempt + 1}次): {url}")

                # 访问URL
                driver.get(url)
                driver.implicitly_wait(5)

                # 获取跳转后的实际URL
                actual_url = driver.current_url
                if actual_url != url:
                    self.log(f"URL发生跳转: {url} -> {actual_url}")

                html = driver.page_source

                # 检查是否为验证码或登录页面
                if self.is_verification_or_login_page(html, actual_url):
                    self.log(f"检测到验证码或登录页面: {actual_url}")
                    if attempt < max_retries:
                        self.log(f"跳过此次尝试，准备重试...")
                        time.sleep(2)  # 等待2秒后重试
                        continue
                    else:
                        self.log(f"达到最大重试次数，跳过URL: {url}")
                        return "", actual_url

                soup = BeautifulSoup(html, 'html.parser')

                # 特殊网站处理
                if 'cfi.net.cn' in actual_url:  # 应对中财网的检测的单独处理
                    element = driver.find_element(By.XPATH, "//input[@type='submit']")
                    element.click()
                    driver.implicitly_wait(5)
                    html = driver.page_source
                    soup = BeautifulSoup(html, 'html.parser')

                elif "baijiahao.baidu" in actual_url:  # 对百家号进行单独处理
                    if soup.find('div', attrs={'data-testid': 'article'}):
                        article = soup.find('div', attrs={'data-testid': 'article'}).text
                        article = textwrap.fill(article, width=80)  # 每行最多80个字符
                        self.log(f"百家号爬取结果：内容长度 {len(article)}")
                        return article, actual_url
                    else:
                        self.log(f"百家号内容未找到: {actual_url}")
                        if attempt < max_retries:
                            self.log(f"内容未找到，准备重试...")
                            time.sleep(2)
                            continue
                        return "", actual_url

                elif 'finance.eastmoney' in actual_url:  # 东方财富网
                    html = re.sub(r'<!--\s*EM_StockImg_Start\s*-->.*?<!--\s*EM_StockImg_End\s*-->', '', html)  # 删除股票部分
                    soup = BeautifulSoup(html, 'html.parser')
                    content_div = soup.find('div', class_='txtinfos')
                    if content_div:
                        paragraphs = [p.get_text(strip=True) for p in content_div.find_all('p') if p.get_text(strip=True)]
                        article = '\n'.join(paragraphs)
                        self.log(f"东方财富网爬取结果：内容长度 {len(article)}")
                        return article, actual_url
                    else:
                        self.log(f"东方财富网内容未找到: {actual_url}")
                        if attempt < max_retries:
                            self.log(f"内容未找到，准备重试...")
                            time.sleep(2)
                            continue
                        return "", actual_url

                elif 'stockstar' in actual_url:  # 证券之星
                    article_div = soup.find('div', class_='article_content')
                    if article_div:
                        article = article_div.get_text(separator='\n', strip=True)
                        self.log(f"证券之星爬取结果：内容长度 {len(article)}")
                        return article, actual_url
                    else:
                        self.log(f"证券之星内容未找到: {actual_url}")
                        if attempt < max_retries:
                            self.log(f"内容未找到，准备重试...")
                            time.sleep(2)
                            continue
                        return "", actual_url

                # 通用网站处理
                else:
                    # 使用GNE提取正文
                    result = extractor.extract(html, noise_node_list=['/html/body/div[contains(@class,"statement")]',
                                                                          '//*[@id="footer"]',
                                                                          '//*[@id="gubaComment"]',
                                                                          '//*[@id="reply_editor"]',
                                                                          '//*[@id="replyList"]',
                                                                          '//div[contains(@class,"zwothers")]'])  # zwothers：东方财富网声明
                    article = result.get('content', '')
                    if article:
                        article = textwrap.fill(article, width=80)
                        self.log(f"GNE提取结果：内容长度 {len(article)}")
                        return article, actual_url
                    else:
                        # 如果GNE提取失败，尝试使用BeautifulSoup提取
                        text = soup.getText(strip=True)
                        if text and len(text.strip()) > 100:  # 确保提取到有效内容
                            text = textwrap.fill(text, width=80)
                            self.log(f"BeautifulSoup提取结果：内容长度 {len(text)}")
                            return text, actual_url
                        else:
                            self.log(f"提取内容过短或为空: {actual_url}")
                            if attempt < max_retries:
                                self.log(f"内容提取失败，准备重试...")
                                time.sleep(2)
                                continue
                            return "", actual_url

            except Exception as e:
                self.log(f"提取内容时出错 (第{attempt + 1}次尝试): {url}, 错误: {str(e)}")
                if attempt < max_retries:
                    self.log(f"准备重试...")
                    time.sleep(2)
                    continue
                else:
                    self.log(f"达到最大重试次数，返回空内容")
                    traceback.print_exc()
                    return "", url  # 如果出错，返回原始URL

        # 如果所有重试都失败，返回空内容
        self.log(f"所有重试都失败，返回空内容: {url}")
        return "", url

    def analyze_content_with_dify(self, content, enterprise, title="", max_retries=3, retry_delay=2):
        """
        使用Dify工作流分析文本内容，进行标签分类和情感分析

        Args:
            content (str): 文本内容
            title (str): 文章标题（可选）
            max_retries (int): 最大重试次数，默认3次
            retry_delay (int): 重试间隔秒数，默认2秒

        Returns:
            dict: 分析结果，包含分类标签和情感分析
        """
        if not self.enable_dify_analysis or not self.dify_analyzer:
            return {
                "success": False,
                "error": "Dify分析未启用",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0}
            }

        if not content or not content.strip():
            return {
                "success": False,
                "error": "网页内容为空",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0}
            }

        try:
            # 使用asyncio.run在同步环境中调用异步函数
            result = asyncio.run(
                self.dify_analyzer.analyze_enterprise_content(
                    content, enterprise, title, "enterprise_analyzer", max_retries, retry_delay
                )
            )

            if result["success"]:
                tags = result["classification"]["tags"]
                polarity = result["sentiment"]["polarity"]
                intensity = result["sentiment"]["intensity"]
                self.log(f"Dify分析成功 - 标签: {tags}, 情感: {polarity}({intensity})")
            else:
                self.log(f"Dify分析失败: {result.get('error', '未知错误')}")

            return result

        except Exception as e:
            error_msg = f"Dify分析调用异常: {str(e)}"
            self.log(error_msg)
            return {
                "success": False,
                "error": "分析异常",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0}
            }



    def get_available_driver_and_extractor(self, timeout=300):
        """
        动态获取空闲的浏览器驱动和提取器实例，如果没有空闲驱动则等待

        Args:
            timeout (int): 等待超时时间（秒），默认5分钟

        Returns:
            tuple: (driver, extractor, driver_index) 浏览器驱动、GNE提取器和驱动索引
        """
        with self.driver_condition:
            start_time = time.time()

            while True:
                # 查找空闲的驱动
                for i, is_busy in enumerate(self.driver_busy):
                    if not is_busy:
                        # 标记为忙碌
                        self.driver_busy[i] = True
                        thread_id = threading.current_thread().ident
                        return self.drivers[i], self.extractors[i], i

                # 检查超时
                if time.time() - start_time > timeout:
                    raise TimeoutError(f"等待空闲驱动超时 ({timeout}秒)")

                # 等待驱动释放
                self.log(f"所有驱动都忙碌，线程 {threading.current_thread().ident} 等待...")
                self.driver_condition.wait(timeout=1)  # 每秒检查一次

    def release_driver(self, driver_index):
        """
        释放驱动，标记为空闲状态

        Args:
            driver_index (int): 驱动索引
        """
        with self.driver_condition:
            if 0 <= driver_index < len(self.driver_busy):
                self.driver_busy[driver_index] = False
                thread_id = threading.current_thread().ident
                # 通知等待的线程
                self.driver_condition.notify_all()
            else:
                self.log(f"警告: 尝试释放无效的驱动索引 {driver_index}")

    def get_driver_status(self):
        """
        获取所有驱动的状态信息

        Returns:
            dict: 驱动状态信息
        """
        with self.driver_lock:
            total_drivers = len(self.drivers)
            busy_count = sum(self.driver_busy)
            free_count = total_drivers - busy_count

            status = {
                'total_drivers': total_drivers,
                'busy_count': busy_count,
                'free_count': free_count,
                'driver_status': [
                    {'index': i, 'busy': busy}
                    for i, busy in enumerate(self.driver_busy)
                ]
            }
            return status

    def get_driver_and_extractor(self, thread_index):
        """
        兼容性方法：根据线程索引获取对应的浏览器驱动和提取器实例
        保留此方法以兼容现有代码，但建议使用动态分配方法

        Args:
            thread_index (int): 线程索引

        Returns:
            tuple: (driver, extractor) 浏览器驱动和GNE提取器
        """
        driver_index = thread_index % len(self.drivers)
        return self.drivers[driver_index], self.extractors[driver_index]

    def generate_pdf_and_save(self, original_url, actual_url, driver):
        """
        将网页转换为PDF并保存到本地文件，使用原始URL的MD5作为文件名

        Args:
            original_url (str): 原始URL（用于生成文件名）
            actual_url (str): 实际访问的URL（用于生成PDF内容）
            driver: 浏览器驱动实例

        Returns:
            str: PDF文件名，如果生成失败返回空字符串
        """
        try:
            # 访问实际URL
            driver.get(actual_url)
            driver.implicitly_wait(5)

            # 使用Chrome的打印功能生成PDF
            print_options = {
                'landscape': False,
                'displayHeaderFooter': False,
                'printBackground': True,
                'preferCSSPageSize': True,
                'paperWidth': 8.27,  # A4纸宽度（英寸）
                'paperHeight': 11.69,  # A4纸高度（英寸）
                'marginTop': 0.4,
                'marginBottom': 0.4,
                'marginLeft': 0.4,
                'marginRight': 0.4
            }

            # 执行打印命令
            result = driver.execute_cdp_cmd('Page.printToPDF', print_options)
            base64_data = result['data']

            # 使用原始URL的MD5哈希值生成文件名
            url_hash = hashlib.md5(original_url.encode('utf-8')).hexdigest()
            pdf_filename = f"{url_hash}.pdf"
            pdf_filepath = os.path.join(self.pdf_output_dir, pdf_filename)

            # 将base64数据解码并保存为PDF文件
            pdf_binary = base64.b64decode(base64_data)
            with open(pdf_filepath, 'wb') as f:
                f.write(pdf_binary)

            self.log(f"PDF保存成功: {pdf_filename}, 文件大小: {len(pdf_binary)} 字节")
            return pdf_filename

        except Exception as e:
            self.log(f"生成PDF时出错: 原始URL={original_url}, 实际URL={actual_url}, 错误: {str(e)}")
            traceback.print_exc()
            return ""

    def save_to_mongodb(self, df_batch):
        """
        将DataFrame批量数据保存到MongoDB，PDF列存储文件名

        Args:
            df_batch (pandas.DataFrame): 要保存的DataFrame数据
        """
        if not self.save_mongodb:
            self.log("跳过保存到MongoDB数据库")
            return

        try:
            if not self.mongo_client:
                self.log("MongoDB客户端未初始化，跳过保存")
                return

            if df_batch.empty:
                self.log("DataFrame为空，跳过MongoDB保存")
                return

            # 将DataFrame转换为字典列表
            records = df_batch.to_dict('records')
            self.log(f"准备保存 {len(records)} 条记录到MongoDB集合: {self.mongo_collection}")

            # 为每条记录添加采集时间
            for record in records:
                record['采集时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 批量插入到MongoDB
            if records:
                result = self.mongo_client.insert_many(records)
                self.log(f"成功保存 {len(records)} 条记录到MongoDB，插入ID数量: {len(result.inserted_ids)}")
                self.log(f"MongoDB集合: {self.mongo_collection}, 数据库: {self.mongo_client.db.name}")
            else:
                self.log("没有记录需要保存到MongoDB")

        except Exception as e:
            self.log(f"保存到MongoDB时出错: {str(e)}")
            self.log(f"MongoDB连接状态: {self.mongo_client is not None}")
            self.log(f"目标集合: {self.mongo_collection}")
            traceback.print_exc()

    def save_to_excel(self, df):
        """
        保存DataFrame到Excel

        Args:
            df (pandas.DataFrame): 要保存的DataFrame
        """
        try:
            # 直接保存到Excel，PDF列现在存储的是文件名
            df.to_excel(self.output_file, index=False)
            self.log(f"已保存数据到Excel: {self.output_file}")

        except Exception as e:
            self.log(f"保存到Excel时出错: {str(e)}")
            traceback.print_exc()

    def is_verification_or_login_page(self, html, actual_url):
        """
        检测是否为验证码页面或登录页面

        Args:
            html (str): 网页HTML内容
            actual_url (str): 实际访问的URL

        Returns:
            bool: 如果是验证码或登录页面返回True
        """
        # 检查URL中的关键词
        url_keywords = ['login', 'captcha', 'verify', 'auth', 'forbidden']
        for keyword in url_keywords:
            if keyword in actual_url.lower():
                return True

        return False

    def close(self):
        """
        关闭所有浏览器驱动和MongoDB连接
        """
        # 关闭所有独立的浏览器驱动
        closed_count = 0
        for i, driver in enumerate(self.drivers):
            try:
                if driver:
                    driver.quit()
                    closed_count += 1
                    self.log(f"浏览器驱动 {i+1} 已关闭")
            except Exception as e:
                self.log(f"关闭浏览器驱动 {i+1} 时出错: {str(e)}")

        # 清空相关资源
        self.drivers.clear()
        self.extractors.clear()
        self.log(f"共关闭了 {closed_count} 个浏览器驱动")

        try:
            if self.mongo_client:
                self.mongo_client.close()
                self.log("MongoDB连接已关闭")
        except Exception as e:
            self.log(f"关闭MongoDB连接时出错: {str(e)}")
    
    def log(self, message):
        """
        记录日志

        Args:
            message (str): 日志消息
        """
        print(message)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")



if __name__ == "__main__":
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='网页内容提取器')
    parser.add_argument('--mode', choices=['enterprise', 'alumni'], default='enterprise',
                       help='运行模式：enterprise(企业) 或 alumni(校友)')
    parser.add_argument('--input-file', type=str, help='输入Excel文件路径')
    parser.add_argument('--output-file', type=str, help='输出Excel文件路径（可以是绝对路径或相对于项目根目录的相对路径）')
    parser.add_argument('--batch-size', type=int, default=20, help='批处理大小')
    parser.add_argument('--disable-dify', action='store_true', help='禁用Dify分析')
    parser.add_argument('--threads', type=int, default=4, help='线程数量，默认为4')
    parser.add_argument('--no-save-excel', action='store_true', help='不保存到Excel文件')
    parser.add_argument('--no-save-mongodb', action='store_true', help='不保存到MongoDB数据库')

    args = parser.parse_args()
    
    try:
        # 根据模式配置不同的参数
        if args.mode == 'enterprise':
            # 企业模式配置
            mode_config = {
                'collection': 'dynamic_enterprise_news',
                'default_input': '企业动态采集结果_2025-07-16 17-34-01.xlsx',
                'output_prefix': '企业动态采集具体结果',
                'description': '企业动态'
            }
        else:  # alumni
            # 校友模式配置
            mode_config = {
                'collection': 'dynamic_alumni_news',
                'default_input': '复旦大学校友动态采集结果_2025-07-09 16-10-27.xlsx',
                'output_prefix': '复旦大学校友动态采集具体结果',
                'description': '校友动态'
            }
        
        # 确定输入文件
        if args.input_file:
            if os.path.isabs(args.input_file):
                input_file = args.input_file
            else:
                input_file = os.path.join(root_path, args.input_file)
        else:
            input_file = os.path.join(root_path, "output", mode_config['default_input'])
        
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件不存在: {input_file}")
            print(f"请指定正确的输入文件路径或确保默认文件存在")
            sys.exit(1)
        
        # 加载数据
        df = pd.read_excel(input_file, sheet_name=0)
        print(f"成功加载{mode_config['description']}数据，共 {len(df)} 条记录")
        
        # 确定输出文件路径
        if args.output_file:
            output_file_path = args.output_file
        else:
            output_file_path = f"{mode_config['output_prefix']}_{datetime.now().strftime('%Y-%m-%d %H-%M-%S')}.xlsx"
        
        # 创建提取器实例
        extractor = WebContentExtractor(
            output_file_path,
            use_proxy=False,
            batch_size=args.batch_size,
            mongo_collection=mode_config['collection'],
            dify_api_url="http://*************/v1",  # 本地Dify实例URL
            dify_api_key="app-ftM9txPiU8gFZ9cMMRe3kB57",  # 替换为实际的API密钥
            enable_dify_analysis=not args.disable_dify,  # 根据参数决定是否启用Dify分析
            thread_count=args.threads,  # 线程数量
            save_excel=not args.no_save_excel,  # 是否保存到Excel
            save_mongodb=not args.no_save_mongodb  # 是否保存到MongoDB
        )

        print(f"开始处理{mode_config['description']}内容...")
        print(f"模式: {args.mode}")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file_path}")
        print(f"MongoDB集合: {mode_config['collection']}")
        print(f"批处理大小: {args.batch_size}")
        print(f"线程数量: {args.threads}")
        print(f"Dify分析: {'启用' if not args.disable_dify else '禁用'}")
        print(f"保存到Excel: {'是' if not args.no_save_excel else '否'}")
        print(f"保存到MongoDB: {'是' if not args.no_save_mongodb else '否'}")
        print("-" * 50)
        
        try:
            result_df = extractor.process_dataframe(df)

            # 根据配置显示保存状态
            save_status = []
            if extractor.save_excel:
                save_status.append("Excel文件")
            if extractor.save_mongodb:
                save_status.append("MongoDB数据库")

            if save_status:
                print(f"{mode_config['description']}内容处理完成，结果已保存到{' 和 '.join(save_status)}")
            else:
                print(f"{mode_config['description']}内容处理完成，未保存到任何存储")
        finally:
            extractor.close()

    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        traceback.print_exc()