# 网页内容提取API文档

## 概述

网页内容提取API是基于Flask框架构建的RESTful API服务，封装了`WebContentExtractor`的功能，提供网页内容提取、PDF生成和文本分析等服务。

## 功能特性

- 🌐 **网页内容提取**: 支持多种网站的内容提取，包括新闻网站、财经网站等
- 📄 **PDF生成**: 自动将网页转换为PDF文件并保存到本地
- 🤖 **智能分析**: 集成Dify工作流进行内容标签分类和情感分析
- 🔄 **批量处理**: 支持单个和批量URL处理
- 💾 **数据存储**: 支持MongoDB数据库存储和Excel文件导出
- 🚀 **高性能**: 多线程处理，动态驱动分配机制

## 快速开始

### 1. 安装依赖

```bash
# 使用uv包管理工具安装依赖
uv sync

# 或使用pip安装
pip install -r requirements.txt

# 如果缺少Flask依赖，单独安装
uv add flask flask-cors
# 或
pip install flask flask-cors
```

### 2. 启动API服务

**方法一：使用启动脚本（推荐）**
```bash
python start_api.py
```

**方法二：直接启动**
```bash
cd src
python web_content_api.py
```

服务将在 `http://localhost:5000` 启动。

### 3. 测试API

**快速测试**
```bash
python test_api.py
```

**完整测试示例**
```bash
python src/web_content_api_examples.py
```

## API接口文档

### 基础信息

- **基础URL**: `http://localhost:5000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 接口列表

#### 1. 健康检查

**接口**: `GET /health`

**描述**: 检查API服务健康状态

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-17 15:30:00",
  "extractor_initialized": true,
  "driver_status": {
    "total_drivers": 2,
    "busy_count": 0,
    "free_count": 2
  }
}
```

#### 2. 获取状态信息

**接口**: `GET /status`

**描述**: 获取提取器详细状态信息

**响应示例**:
```json
{
  "success": true,
  "extractor_initialized": true,
  "driver_status": {
    "total_drivers": 2,
    "busy_count": 0,
    "free_count": 2
  },
  "dify_analysis_enabled": true,
  "mongo_collection": "api_web_content_data",
  "timestamp": "2025-07-17 15:30:00"
}
```

#### 3. 单个URL内容提取

**接口**: `POST /extract`

**描述**: 提取单个URL的网页内容

**请求参数**:
```json
{
  "url": "https://example.com/article",
  "enterprise_name": "企业名称",
  "enterprise_id": "企业ID"
}
```

**参数说明**:
- `url` (string, 必填): 要提取内容的网页URL
- `enterprise_name` (string, 可选): 企业名称
- `enterprise_id` (string, 可选): 企业ID

**响应示例**:
```json
{
  "success": true,
  "url": "https://example.com/article",
  "enterprise_name": "企业名称",
  "enterprise_id": "企业ID",
  "网页内容": "提取的网页文本内容...",
  "网页pdf": "abc123def456.pdf",
  "实际访问URL": "https://example.com/article",
  "内容标签": "科技, 创新, 发展",
  "情感极性": "Positive",
  "情感强度": 0.8,
  "细粒度分析": "技术创新:0.9; 市场前景:0.7",
  "timestamp": "2025-07-17 15:30:00",
  "error": ""
}
```

#### 4. 批量URL内容提取

**接口**: `POST /batch_extract`

**描述**: 批量提取多个URL的网页内容

**请求参数**:
```json
{
  "urls": [
    {
      "url": "https://example1.com/article1",
      "enterprise_name": "企业1",
      "enterprise_id": "id1"
    },
    {
      "url": "https://example2.com/article2",
      "enterprise_name": "企业2",
      "enterprise_id": "id2"
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "total_count": 2,
  "success_count": 2,
  "failed_count": 0,
  "results": [
    {
      "success": true,
      "url": "https://example1.com/article1",
      "网页内容": "内容1...",
      "网页pdf": "file1.pdf"
    },
    {
      "success": true,
      "url": "https://example2.com/article2",
      "网页内容": "内容2...",
      "网页pdf": "file2.pdf"
    }
  ],
  "timestamp": "2025-07-17 15:30:00"
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": "错误描述信息",
  "timestamp": "2025-07-17 15:30:00"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 接口不存在
- `500 Internal Server Error`: 服务器内部错误

## 配置说明

### 环境配置

API服务的主要配置项在 `web_content_api.py` 中：

```python
# Dify分析配置
dify_api_url="http://*************/v1"
dify_api_key="app-ftM9txPiU8gFZ9cMMRe3kB57"

# MongoDB配置
mongo_collection="api_web_content_data"

# 线程配置
thread_count=2  # API模式使用较少线程数
```

### 输出目录

- **Excel文件**: `output/` 目录
- **PDF文件**: `output/pdf/` 目录
- **日志文件**: `log/网页内容爬取日志/` 目录

## 使用示例

### Python示例

```python
import requests
import json

# 单个URL提取
def extract_single_url():
    url = "http://localhost:5000/extract"
    data = {
        "url": "https://finance.sina.com.cn/tech/example.shtml",
        "enterprise_name": "新浪科技",
        "enterprise_id": "sina_tech_001"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result['success']:
        print(f"内容长度: {len(result['网页内容'])}")
        print(f"PDF文件: {result['网页pdf']}")
        print(f"情感分析: {result['情感极性']} ({result['情感强度']})")
    else:
        print(f"提取失败: {result['error']}")

# 批量URL提取
def extract_batch_urls():
    url = "http://localhost:5000/batch_extract"
    data = {
        "urls": [
            {"url": "https://example1.com", "enterprise_name": "企业1"},
            {"url": "https://example2.com", "enterprise_name": "企业2"}
        ]
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"成功处理: {result['success_count']}/{result['total_count']}")
```

### curl示例

```bash
# 健康检查
curl -X GET http://localhost:5000/health

# 单个URL提取
curl -X POST http://localhost:5000/extract \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/article",
    "enterprise_name": "测试企业",
    "enterprise_id": "test_001"
  }'

# 批量URL提取
curl -X POST http://localhost:5000/batch_extract \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      {"url": "https://example1.com", "enterprise_name": "企业1"},
      {"url": "https://example2.com", "enterprise_name": "企业2"}
    ]
  }'
```

## 注意事项

1. **资源管理**: API服务会自动管理浏览器驱动资源，服务关闭时会自动清理
2. **并发限制**: 默认使用2个浏览器驱动，可根据服务器性能调整
3. **超时设置**: 单个URL处理超时时间为5分钟
4. **文件存储**: PDF文件使用URL的MD5哈希值作为文件名，避免重复
5. **日志记录**: 所有操作都会记录详细日志，便于问题排查

## 故障排除

### 常见问题

1. **浏览器驱动初始化失败**
   - 检查Chrome浏览器是否正确安装
   - 确认ChromeDriver版本与Chrome版本匹配

2. **Dify分析失败**
   - 检查Dify服务是否正常运行
   - 验证API密钥是否正确

3. **MongoDB连接失败**
   - 确认MongoDB服务是否启动
   - 检查连接配置是否正确

### 日志查看

日志文件位于 `log/网页内容爬取日志/` 目录，包含详细的操作记录和错误信息。

## 更新日志

- **v1.0.0** (2025-07-17): 初始版本发布，支持基础的网页内容提取和API接口功能
