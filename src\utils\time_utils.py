# -*- coding: utf-8 -*-
"""
时间处理工具模块

本模块提供时间解析、转换和格式化相关的功能，包括：
- 多种时间格式的正则匹配
- 相对时间转换为绝对时间
- 时间字符串解析和格式化
"""

import re
import time
from datetime import datetime, timedelta


class TimeUtils:
    """时间处理工具类，提供各种时间解析和转换功能"""
    
    def __init__(self):
        """初始化时间工具类，预构建时间匹配模式"""
        self.match_item, self.match_pattern, self.control_char_re = self._get_matches()
    
    def _get_matches(self):
        """
        构建各种日期格式的正则匹配模式
        
        Returns:
            tuple: (match_item, match_pattern, control_char_re)
                - match_item: 匹配项列表
                - match_pattern: 组合的正则匹配模式
                - control_char_re: 控制字符正则表达式
        """
        match_item = []
        
        # 匹配正则表达式
        matches = {
            1: (r'\d{4}%s\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s\d{1,2}%s', '%%Y%s%%m%s%%d%s %%H%s%%M%s%%S%s'),
            2: (r'\d{4}%s\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s', '%%Y%s%%m%s%%d%s %%H%s%%M%s'),
            3: (r'\d{4}%s\d{1,2}%s\d{1,2}%s', '%%Y%s%%m%s%%d%s'),
            4: (r'\d{2}%s\d{1,2}%s\d{1,2}%s', '%%y%s%%m%s%%d%s'),
            # 没有年份
            5: (r'\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s\d{1,2}%s', '%%m%s%%d%s %%H%s%%M%s%%S%s'),
            6: (r'\d{1,2}%s\d{1,2}%s \d{1,2}%s\d{1,2}%s', '%%m%s%%d%s %%H%s%%M%s'),
            7: (r'\d{1,2}%s\d{1,2}%s', '%%m%s%%d%s'),
            # 没有年月日
            8: (r'\d{1,2}%s\d{1,2}%s\d{1,2}%s', '%%H%s%%M%s%%S%s'),
            9: (r'\d{1,2}%s\d{1,2}%s', '%%H%s%%M%s'),
        }
        
        # 正则中的%s分割符
        splits = [
            {1: [('年', '月', '日', '点', '分', '秒'), ('-', '-', '', ':', ':', ''), (r'\/', r'\/', '', ':', ':', ''),
                 (r'\.', r'\.', '', ':', ':', '')]},
            {2: [('年', '月', '日', '点', '分'), ('-', '-', '', ':', ''), (r'\/', r'\/', '', ':', ''),
                 (r'\.', r'\.', '', ':', '')]},
            {3: [('年', '月', '日'), ('-', '-', ''), (r'\/', r'\/', ''), (r'\.', r'\.', '')]},
            {4: [('年', '月', '日'), ('-', '-', ''), (r'\/', r'\/', ''), (r'\.', r'\.', '')]},
            # 没有年份
            {5: [('月', '日', '点', '分', '秒'), ('-', '', ':', ':', ''), (r'\/', '', ':', ':', ''),
                 (r'\.', '', ':', ':', '')]},
            {6: [('月', '日', '点', '分'), ('-', '', ':', ''), (r'\/', '', ':', ''), (r'\.', '', ':', '')]},
            {7: [('月', '日'), ('-', ''), (r'\/', ''), (r'\.', '')]},
            # 没有年月日
            {8: [('点', '分', '秒'), (':', ':', '')]},
            {9: [('点', '分'), (':', '')]},
        ]
        
        # 构建穷举正则匹配公式及提取的字符串转datetime格式映射
        for item in splits:
            for num, value in item.items():
                match = matches[num]
                for sp in value:
                    tmp = []
                    for m in match:
                        tmp.append(m % sp)
                    match_item.append(tuple(tmp))
        
        match_pattern = '|'.join([x[0] for x in match_item])
        control_uni_char = list(range(0, 9)) + list(range(11, 13)) + list(range(14, 32))
        control_char_re = re.compile('[%s]' % re.escape(''.join(map(chr, control_uni_char))))
        
        return match_item, match_pattern, control_char_re
    
    def transform_time(self, text, base_time=None):
        """
        将相对时间转换为绝对日期
        
        Args:
            text (str): 包含相对时间的文本
            base_time (datetime, optional): 基准时间，默认为当前时间
            
        Returns:
            str: 转换后的文本，相对时间已替换为绝对时间
        """
        if base_time is None:
            base_time = datetime.now()
            
        times = re.findall(r'\d+个月前|\d+月前|\d+周前|\d+星期前|\d+天前|\d+分钟前|\d+小时前|\d+秒前|今天|昨天|前天|刚刚', text)
        
        for t in times:
            if '昨天' in t:
                text = text.replace(t, datetime.strftime(base_time - timedelta(days=1), '%Y-%m-%d '))
            elif '前天' in t:
                text = text.replace(t, datetime.strftime(base_time - timedelta(days=2), '%Y-%m-%d '))
            elif '今天' in t:
                text = text.replace(t, datetime.strftime(base_time, '%Y-%m-%d '))
            elif '天前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(days=int(num)), '%Y-%m-%d '))
            elif '周前' in t or '星期前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(days=int(num) * 7), '%Y-%m-%d '))
            elif '月前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(days=int(num) * 30), '%Y-%m-%d '))
            elif '小时前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(hours=int(num)), '%Y-%m-%d %H:%M:%S'))
            elif '分钟前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(minutes=int(num)), '%Y-%m-%d %H:%M:%S'))
            elif '秒前' in t:
                num = re.match(r'\d+', t).group()
                text = text.replace(t, datetime.strftime(base_time - timedelta(seconds=int(num)), '%Y-%m-%d %H:%M:%S'))
            elif '刚刚' in t:
                text = datetime.strftime(base_time, '%Y-%m-%d %H:%M:%S')
                
        return ' '.join(text.split())
    
    def parse_time(self, text, base_time=None):
        """
        格式化日期字符串，将各种格式的时间转换为标准格式
        
        Args:
            text (str): 包含时间信息的文本
            base_time (datetime, optional): 基准时间，默认为当前时间
            
        Returns:
            str: 解析后的日期字符串，格式为'%Y-%m-%d'，解析失败返回空字符串
        """
        if base_time is None:
            base_time = datetime.now()
            
        text = self.transform_time(text, base_time)
        res = []
        match_list = re.findall(self.match_pattern, text)
        
        if not match_list:
            return ''
            
        for match in match_list:
            for i in range(len(self.match_item)):
                try:
                    date = datetime.strptime(match, self.match_item[i][1].replace('\\', ''))
                    if i + 1 >= 17:  # 没有年份
                        date = date.replace(year=base_time.year)
                        if i + 1 >= 29:  # 没有年月日
                            date = date.replace(month=base_time.month).replace(day=base_time.day)
                    res.append(datetime.strftime(date, '%Y-%m-%d'))
                    break
                except:
                    pass
                    
        if not res:
            return ''
        return res[0]
    
    @staticmethod
    def within_days(date_str, keep_days):
        """
        检查日期是否在指定天数范围内

        Args:
            date_str (str): 日期字符串，格式为'%Y-%m-%d'
            keep_days (int): 保留天数

        Returns:
            bool: 如果日期在指定天数内返回True，否则返回False
        """
        if not date_str:
            return False

        try:
            date = datetime.strptime(date_str, '%Y-%m-%d')
            if datetime.now() - date <= timedelta(days=keep_days):
                return True
            else:
                return False
        except ValueError:
            return False
