mongodb_text_input="""
{
              "name": "tyc_bak240701",
              "displayName": "",
              "id": "#/definitions/181425829",
              "description": "",
              "schema": {
                "jsonSchema": {
                  "properties": {
                    "_id": {
                      "type": "integer"
                    },
                    "regStatus": {
                      "type": "string"
                    },
                    "gid": {
                      "type": "integer"
                    },
                    "regCapital": {
                      "type": "string"
                    },
                    "showEmail": {
                      "type": "string"
                    },
                    "city": {
                      "type": "null"
                    },
                    "attrTitle": {
                      "properties": {
                        "legalPersonTitle": {
                          "type": "string"
                        },
                        "regCapitalTitle": {
                          "type": "string"
                        },
                        "establishTimeTitle": {
                          "type": "string"
                        }
                      },
                      "type": "object",
                      "x-apifox-orders": [
                        "legalPersonTitle",
                        "regCapitalTitle",
                        "establishTimeTitle"
                      ]
                    },
                    "emailList": {
                      "type": "array",
                      "items": {
                        "type": "string"
                      }
                    },
                    "showPhone": {
                      "type": "string"
                    },
                    "establish_time_long": {
                      "type": "string"
                    },
                    "claimLevel": {
                      "type": "integer"
                    },
                    "recallSrc": {
                      "type": "string"
                    },
                    "score": {
                      "type": "string"
                    },
                    "legalPersonType": {
                      "type": "integer"
                    },
                    "province": {
                      "type": "null"
                    },
                    "establishTime": {
                      "type": "string"
                    },
                    "param_reg_capital": {
                      "type": "string"
                    },
                    "legalPerson": {
                      "type": "string"
                    },
                    "logo": {
                      "type": "string"
                    },
                    "alias": {
                      "type": "string"
                    },
                    "contentType": {
                      "type": "integer"
                    },
                    "hasMorePhone": {
                      "type": "boolean"
                    },
                    "legalPersonGid": {
                      "type": "integer"
                    },
                    "phoneType": {
                      "type": "integer"
                    },
                    "entityType": {
                      "type": "integer"
                    },
                    "regLocation": {
                      "type": "string"
                    },
                    "phoneTips": {
                      "type": "string"
                    },
                    "tagList": {
                      "type": "array",
                      "items": {
                        "properties": {
                          "type": {
                            "type": "integer"
                          },
                          "value": {
                            "type": "string"
                          },
                          "title": {
                            "type": "string"
                          },
                          "color": {
                            "type": "string"
                          },
                          "background": {
                            "type": "string"
                          }
                        },
                        "type": "object",
                        "x-apifox-orders": [
                          "type",
                          "value",
                          "title",
                          "color",
                          "background"
                        ]
                      }
                    },
                    "illegalSocialOrganization": {
                      "type": "null"
                    },
                    "name": {
                      "type": "string"
                    },
                    "phoneTagType": {
                      "type": "integer"
                    },
                    "matchField": {
                      "type": "null"
                    },
                    "base": {
                      "type": "string"
                    }
                  },
                  "type": "object",
                  "x-apifox-orders": [
                    "_id",
                    "regStatus",
                    "gid",
                    "regCapital",
                    "showEmail",
                    "city",
                    "attrTitle",
                    "emailList",
                    "showPhone",
                    "establish_time_long",
                    "claimLevel",
                    "recallSrc",
                    "score",
                    "legalPersonType",
                    "province",
                    "establishTime",
                    "param_reg_capital",
                    "legalPerson",
                    "logo",
                    "alias",
                    "contentType",
                    "hasMorePhone",
                    "legalPersonGid",
                    "phoneType",
                    "entityType",
                    "regLocation",
                    "phoneTips",
                    "tagList",
                    "illegalSocialOrganization",
                    "name",
                    "phoneTagType",
                    "matchField",
                    "base"
                  ]
                }
              },
              "visibility": "INHERITED",
              "moduleId": 5745998
            }
"""

mongodb_text_output="""
{
  "name": "tyc_bak240701",
  "displayName": "",
  "id": "#/definitions/181425829",
  "description": "",
  "properties": {
    "天眼查240701": {
      "properties": {
        "_id": {
          "type": "integer",
          "title": "唯一ID",
          "description": "记录的唯一标识符"
        },
        "regStatus": {
          "type": "string",
          "title": "注册状态",
          "description": "企业的注册状态（如存续、注销等）"
        },
        "gid": {
          "type": "integer",
          "title": "集团ID",
          "description": "企业所属集团的ID"
        },
        "regCapital": {
          "type": "string",
          "title": "注册资本",
          "description": "企业注册资本的金额"
        },
        "showEmail": {
          "type": "string",
          "title": "显示邮箱",
          "description": "对外展示的邮箱地址"
        },
        "city": {
          "type": "null",
          "title": "城市",
          "description": "企业所在城市"
        },
        "attrTitle": {
          "properties": {
            "legalPersonTitle": {
              "type": "string",
              "title": "法人代表标题",
              "description": "法人代表字段的显示标题"
            },
            "regCapitalTitle": {
              "type": "string",
              "title": "注册资本标题",
              "description": "注册资本字段的显示标题"
            },
            "establishTimeTitle": {
              "type": "string",
              "title": "成立时间标题",
              "description": "成立时间字段的显示标题"
            }
          },
          "type": "object",
          "x-apifox-orders": [
            "legalPersonTitle",
            "regCapitalTitle",
            "establishTimeTitle"
          ]
        },
        "emailList": {
          "type": "array",
          "title": "邮箱列表",
          "description": "企业关联的邮箱地址列表",
          "items": {
            "type": "string"
          }
        },
        "showPhone": {
          "type": "string",
          "title": "显示电话",
          "description": "对外展示的电话号码"
        },
        "establish_time_long": {
          "type": "string",
          "title": "成立时间(长格式)",
          "description": "企业成立的详细时间"
        },
        "claimLevel": {
          "type": "integer",
          "title": "认领等级",
          "description": "企业信息认领等级"
        },
        "recallSrc": {
          "type": "string",
          "title": "召回来源",
          "description": "数据召回来源"
        },
        "score": {
          "type": "string",
          "title": "评分",
          "description": "企业信用/质量评分"
        },
        "legalPersonType": {
          "type": "integer",
          "title": "法人类型",
          "description": "法人代表类型标识"
        },
        "province": {
          "type": "null",
          "title": "省份",
          "description": "企业所在省份"
        },
        "establishTime": {
          "type": "string",
          "title": "成立时间",
          "description": "企业成立时间"
        },
        "param_reg_capital": {
          "type": "string",
          "title": "注册资本参数",
          "description": "注册资本的参数化表示"
        },
        "legalPerson": {
          "type": "string",
          "title": "法人代表",
          "description": "企业法人代表姓名"
        },
        "logo": {
          "type": "string",
          "title": "企业Logo",
          "description": "企业Logo的URL地址"
        },
        "alias": {
          "type": "string",
          "title": "别名",
          "description": "企业别名/简称"
        },
        "contentType": {
          "type": "integer",
          "title": "内容类型",
          "description": "企业内容类型标识"
        },
        "hasMorePhone": {
          "type": "boolean",
          "title": "有更多电话",
          "description": "是否还有更多电话号码"
        },
        "legalPersonGid": {
          "type": "integer",
          "title": "法人集团ID",
          "description": "法人所属集团的ID"
        },
        "phoneType": {
          "type": "integer",
          "title": "电话类型",
          "description": "电话号码的类型标识"
        },
        "entityType": {
          "type": "integer",
          "title": "实体类型",
          "description": "企业实体类型标识"
        },
        "regLocation": {
          "type": "string",
          "title": "注册地址",
          "description": "企业注册地址"
        },
        "phoneTips": {
          "type": "string",
          "title": "电话提示",
          "description": "电话号码的相关提示信息"
        },
        "tagList": {
          "type": "array",
          "title": "标签列表",
          "description": "企业关联的标签列表",
          "items": {
            "properties": {
              "type": {
                "type": "integer",
                "title": "标签类型",
                "description": "标签的类型标识"
              },
              "value": {
                "type": "string",
                "title": "标签值",
                "description": "标签的实际值"
              },
              "title": {
                "type": "string",
                "title": "标签标题",
                "description": "标签的显示标题"
              },
              "color": {
                "type": "string",
                "title": "标签颜色",
                "description": "标签的字体颜色"
              },
              "background": {
                "type": "string",
                "title": "标签背景色",
                "description": "标签的背景颜色"
              }
            },
            "type": "object",
            "x-apifox-orders": [
              "type",
              "value",
              "title",
              "color",
              "background"
            ]
          }
        },
        "illegalSocialOrganization": {
          "type": "null",
          "title": "非法社会组织",
          "description": "是否被标记为非法社会组织"
        },
        "name": {
          "type": "string",
          "title": "企业名称",
          "description": "企业的全称"
        },
        "phoneTagType": {
          "type": "integer",
          "title": "电话标签类型",
          "description": "电话号码标签的类型标识"
        },
        "matchField": {
          "type": "null",
          "title": "匹配字段",
          "description": "数据匹配的关键字段"
        },
        "base": {
          "type": "string",
          "title": "基础信息",
          "description": "企业的基础信息摘要"
        }
      },
      "type": "object",
      "x-apifox-orders": [
        "_id",
        "regStatus",
        "gid",
        "regCapital",
        "showEmail",
        "city",
        "attrTitle",
        "emailList",
        "showPhone",
        "establish_time_long",
        "claimLevel",
        "recallSrc",
        "score",
        "legalPersonType",
        "province",
        "establishTime",
        "param_reg_capital",
        "legalPerson",
        "logo",
        "alias",
        "contentType",
        "hasMorePhone",
        "legalPersonGid",
        "phoneType",
        "entityType",
        "regLocation",
        "phoneTips",
        "tagList",
        "illegalSocialOrganization",
        "name",
        "phoneTagType",
        "matchField",
        "base"
      ]
    }
  },
  "visibility": "INHERITED",
  "moduleId": 5745998
}
"""

oracle_text_input="""{
              "name": "MV_AI_KRM_ALUMNI_BULLETIN",
              "displayName": "MV_AI_KRM_ALUMNI_BULLETIN",
              "id": "#/definitions/181880510",
              "description": "",
              "schema": {
                "jsonSchema": {
                  "title": "MV_AI_KRM_ALUMNI_BULLETIN",
                  "type": "object",
                  "properties": {
                    "KID": {
                      "type": "string",
                      "description": "KID",
                      "maxLength": 100,
                      "examples": [
                        "ff80808180cc880501811e84dda20015030401"
                      ]
                    },
                    "ALUMNI_ID": {
                      "type": "string",
                      "description": "ALUMNI_ID",
                      "maxLength": 100,
                      "examples": [
                        "ff8080818572b7ac018572c4e43f4242"
                      ]
                    },
                    "BULLETIN_SUBJECT": {
                      "type": "string",
                      "description": "BULLETIN_SUBJECT",
                      "maxLength": 1000,
                      "examples": [
                        "华东师范大学校友动态简报（2022年06月01日）"
                      ]
                    },
                    "BULLETIN_TYPE": {
                      "type": "string",
                      "description": "BULLETIN_TYPE",
                      "maxLength": 12,
                      "examples": [
                        "校友简报"
                      ]
                    },
                    "BULLETIN_SCHOOL": {
                      "type": "string",
                      "description": "BULLETIN_SCHOOL",
                      "maxLength": 200,
                      "examples": [
                        "华东师范大学"
                      ]
                    },
                    "BULLETIN_NUM": {
                      "type": "string",
                      "description": "BULLETIN_NUM",
                      "maxLength": 100,
                      "examples": [
                        "2022年06月01日"
                      ]
                    },
                    "ITEM_NAME": {
                      "type": "string",
                      "description": "ITEM_NAME",
                      "maxLength": 200,
                      "examples": [
                        "观点见解"
                      ]
                    },
                    "BULLETIN_NEWS_TITLE": {
                      "type": "string",
                      "description": "BULLETIN_NEWS_TITLE",
                      "maxLength": 2000,
                      "examples": [
                        "王殿军：科学教育如何更上层楼"
                      ]
                    },
                    "BULLETIN_NEWS_TIME": {
                      "type": "string",
                      "description": "BULLETIN_NEWS_TIME",
                      "format": "date-time",
                      "examples": [
                        "2022-05-13T00:00:00Z"
                      ]
                    },
                    "BULLETIN_NEWS_LINK": {
                      "type": "string",
                      "description": "BULLETIN_NEWS_LINK",
                      "maxLength": 1000,
                      "examples": [
                        "http://paper.jyb.cn/zgjyb/html/2022-05/13/content_609396.htm?div=-1"
                      ]
                    },
                    "BULLETIN_NEWS_INTRO": {
                      "type": "string",
                      "description": "BULLETIN_NEWS_INTRO",
                      "maxLength": 4000,
                      "examples": [
                        "示例BULLETIN_NEWS_INTRO"
                      ]
                    },
                    "BULLETIN_ENTITY_NAME": {
                      "type": "string",
                      "description": "BULLETIN_ENTITY_NAME",
                      "maxLength": 400,
                      "examples": [
                        "王殿军"
                      ]
                    },
                    "BULLETIN_ENTITY_INTRO": {
                      "type": "string",
                      "description": "BULLETIN_ENTITY_INTRO",
                      "maxLength": 4000,
                      "examples": [
                        "教育部中学校长培训中心全国优秀中学校长高级研究班学员"
                      ]
                    }
                  },
                  "required": [
                    "KID"
                  ],
                  "x-apifox-orders": [
                    "KID",
                    "ALUMNI_ID",
                    "BULLETIN_SUBJECT",
                    "BULLETIN_TYPE",
                    "BULLETIN_SCHOOL",
                    "BULLETIN_NUM",
                    "ITEM_NAME",
                    "BULLETIN_NEWS_TITLE",
                    "BULLETIN_NEWS_TIME",
                    "BULLETIN_NEWS_LINK",
                    "BULLETIN_NEWS_INTRO",
                    "BULLETIN_ENTITY_NAME",
                    "BULLETIN_ENTITY_INTRO"
                  ]
                }
              },
              "visibility": "INHERITED",
              "moduleId": 5745998
            }"""

oracle_text_output=""" {
              "name": "MV_AI_KRM_ALUMNI_BULLETIN",
              "displayName": "MV_AI_KRM_ALUMNI_BULLETIN",
              "id": "#/definitions/181880510",
              "description": "",
              "properties": {
                "MV_AI_KRM_ALUMNI_BULLETIN": {
                  "title": "MV_AI_KRM_ALUMNI_BULLETIN",
                  "type": "object",
                  "properties": {
                    "KID": {
                      "type": "string",
                      "description": "主键，唯一标识一条简报记录，通常为字符串ID",
                      "maxLength": 100,
                      "examples": [
                        "ff80808180cc880501811e84dda20015030401"
                      ],
                      "title": "主键"
                    },
                    "ALUMNI_ID": {
                      "type": "string",
                      "description": "关联的校友ID，指向校友的唯一标识",
                      "maxLength": 100,
                      "examples": [
                        "ff8080818572b7ac018572c4e43f4242"
                      ],
                      "title": "校友ID"
                    },
                    "BULLETIN_SUBJECT": {
                      "type": "string",
                      "description": "简报的主题或标题，描述本期简报的主要内容",
                      "maxLength": 1000,
                      "examples": [
                        "华东师范大学校友动态简报（2022年06月01日）"
                      ],
                      "title": "简报主题"
                    },
                    "BULLETIN_TYPE": {
                      "type": "string",
                      "description": "简报的类型，例如校友简报",
                      "maxLength": 12,
                      "examples": [
                        "校友简报"
                      ],
                      "title": "简报类型"
                    },
                    "BULLETIN_SCHOOL": {
                      "type": "string",
                      "description": "简报所属学校名称",
                      "maxLength": 200,
                      "examples": [
                        "华东师范大学"
                      ],
                      "title": "所属学校"
                    },
                    "BULLETIN_NUM": {
                      "type": "string",
                      "description": "简报的编号或日期标识",
                      "maxLength": 100,
                      "examples": [
                        "2022年06月01日"
                      ],
                      "title": "简报编号"
                    },
                    "ITEM_NAME": {
                      "type": "string",
                      "description": "简报中的栏目名称，例如观点见解",
                      "maxLength": 200,
                      "examples": [
                        "观点见解"
                      ],
                      "title": "栏目名称"
                    },
                    "BULLETIN_NEWS_TITLE": {
                      "type": "string",
                      "description": "简报中新闻的标题",
                      "maxLength": 2000,
                      "examples": [
                        "王殿军：科学教育如何更上层楼"
                      ],
                      "title": "新闻标题"
                    },
                    "BULLETIN_NEWS_TIME": {
                      "type": "string",
                      "description": "新闻的发布时间，格式为日期时间",
                      "format": "date-time",
                      "examples": [
                        "2022-05-13T00:00:00Z"
                      ],
                      "title": "新闻时间"
                    },
                    "BULLETIN_NEWS_LINK": {
                      "type": "string",
                      "description": "新闻的链接地址",
                      "maxLength": 1000,
                      "examples": [
                        "http://paper.jyb.cn/zgjyb/html/2022-05/13/content_609396.htm?div=-1"
                      ],
                      "title": "新闻链接"
                    },
                    "BULLETIN_NEWS_INTRO": {
                      "type": "string",
                      "description": "新闻的简介或摘要",
                      "maxLength": 4000,
                      "examples": [
                        "示例BULLETIN_NEWS_INTRO"
                      ],
                      "title": "新闻简介"
                    },
                    "BULLETIN_ENTITY_NAME": {
                      "type": "string",
                      "description": "新闻中涉及的人物或实体名称",
                      "maxLength": 400,
                      "examples": [
                        "王殿军"
                      ],
                      "title": "实体名称"
                    },
                    "BULLETIN_ENTITY_INTRO": {
                      "type": "string",
                      "description": "新闻中涉及的人物或实体的简介",
                      "maxLength": 4000,
                      "examples": [
                        "教育部中学校长培训中心全国优秀中学校长高级研究班学员"
                      ],
                      "title": "实体简介"
                    }
                  },
                  "required": [
                    "KID"
                  ],
                  "x-apifox-orders": [
                    "KID",
                    "ALUMNI_ID",
                    "BULLETIN_SUBJECT",
                    "BULLETIN_TYPE",
                    "BULLETIN_SCHOOL",
                    "BULLETIN_NUM",
                    "ITEM_NAME",
                    "BULLETIN_NEWS_TITLE",
                    "BULLETIN_NEWS_TIME",
                    "BULLETIN_NEWS_LINK",
                    "BULLETIN_NEWS_INTRO",
                    "BULLETIN_ENTITY_NAME",
                    "BULLETIN_ENTITY_INTRO"
                  ]
                }
              },
              "visibility": "INHERITED",
              "moduleId": 5745998
            },"""

mongodb_prompt=f"""
你是一个专门处理MongoDB表结构描述的助手，接下来会提供一个JSON文本，这个json文本描述了一个MongoDB数据库表的所有数据字段和表结构，需严格按以下规则处理JSON文本：

处理规则：
1. 字段增强：
   - 为每个字段添加：
     - `title`：中文名称（结合上下文语义生成）
     - `description`：字段详细说明
2. 嵌套处理：
   - 对嵌套对象（包括`object`类型）及其子字段递归应用上述增强
3. 字段重命名：
   - 将`schema`字段改名为`properties`
   - 将`schema.jsonSchema`改名为表中文名称
4. 特殊说明：
   - "tyc"需解释为"天眼查"
   - 严格保持原始JSON结构和字段顺序
   - 表的中文名称在name字段中

输出要求：
1. 仅返回处理后的标准JSON文本
2. 禁止包含任何解释性文字或注释
下面是输入示例：
{mongodb_text_input}
下面是输出示例：
{mongodb_text_output}
"""

oracle_prompt=f"""
你是一个专门处理Oracle表结构描述的助手，接下来会提供一个JSON文本，这个json文本描述了一个Oracle数据库表的所有数据字段和表结构，需严格按以下规则处理JSON文本：

处理规则：
1. 字段增强：
   - 为每个字段添加：
     - `title`：中文名称（结合上下文语义生成）
     - `description`：字段详细说明
2. 嵌套处理：
   - 对嵌套对象（包括`object`类型）及其子字段递归应用上述增强
3. 字段重命名：
   - 将`schema`字段改名为`properties`
   - 将`schema.jsonSchema`改名为表中文名称
4. 特殊说明：
   - "tyc"需解释为"天眼查"
   - 严格保持原始JSON结构和字段顺序
   - 表的中文名称在name字段中
输出要求：
1. 仅返回处理后的标准JSON文本
2. 禁止包含任何解释性文字或注释
下面是输入示例：
{oracle_text_input}
下面是输出示例：
{oracle_text_output}
"""
class Config (object):
    """
    Configuration class for prompt settings.
    """
    def __init__(self):
        self.oracle_prompt = oracle_prompt
        self.mongodb_prompt = mongodb_prompt