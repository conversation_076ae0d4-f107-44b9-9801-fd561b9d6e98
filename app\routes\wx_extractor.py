# -*- coding: utf-8 -*-
"""
微信提取器路由模块

本模块提供微信相关的内容提取功能，包括：
- 微信公众号文章提取
- 微信链接内容解析
- 微信文章批量处理
- 微信内容格式化

主要路由：
- /wx/extract - 微信内容提取接口
- /wx/batch-extract - 批量微信内容提取
- /wx/test - 功能测试接口
"""

import sys
import os
import asyncio
import json
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from typing import List, Dict, Any, Optional
from urllib.parse import quote, urlparse

# 添加src/utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'utils'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'service'))

# 导入搜索器和工具类
from service.search import Searcher

# 创建微信提取器路由蓝图
wx_extractor_bp = Blueprint('wx_extractor', __name__, url_prefix='/wx')


class WeChatExtractor(Searcher):
    """
    微信内容提取器

    专门用于提取微信公众号文章内容的提取器实现
    继承自Searcher基类，针对微信内容提取进行优化
    """

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        """
        初始化微信内容提取器

        Args:
            use_proxy (bool): 是否使用代理
            proxy_config (dict): 代理配置
        """
        super().__init__(use_proxy, proxy_config)
        self.wechat_domains = ['mp.weixin.qq.com', 'weixin.qq.com']

    def build_search_url(self, query: str, **kwargs) -> str:
        """
        构建微信搜索URL（此方法在微信提取器中不常用）

        Args:
            query (str): 搜索关键词
            **kwargs: 其他参数

        Returns:
            str: 构建的URL
        """
        # 微信提取器主要处理直接的微信链接，不需要构建搜索URL
        return query if self.is_wechat_url(query) else ""

    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        """
        从微信页面提取内容结果

        Args:
            html_content (str): 页面HTML内容

        Returns:
            List[Dict[str, str]]: 提取结果列表
        """
        # 这个方法在微信提取器中主要通过async_search中的extract_content来实现
        return []

    def is_wechat_url(self, url: str) -> bool:
        """
        检查URL是否为微信链接

        Args:
            url (str): 要检查的URL

        Returns:
            bool: 是否为微信链接
        """
        try:
            parsed_url = urlparse(url)
            return any(domain in parsed_url.netloc for domain in self.wechat_domains)
        except Exception:
            return False


@wx_extractor_bp.route('/extract', methods=['GET', 'POST'])
def extract_wechat_content():
    """
    微信内容提取接口

    支持GET和POST请求，用于提取微信公众号文章内容

    Args:
        url (str): 微信文章URL（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）

    Returns:
        JSON: 包含提取结果的响应
            - success (bool): 请求是否成功
            - results (list): 提取结果列表
            - error (str): 错误信息（仅在失败时返回）
    """
    try:
        # 获取请求参数
        if request.method == 'GET':
            url = request.args.get('url')
            use_proxy = request.args.get('use_proxy', 'false').lower() == 'true'
            max_retries = int(request.args.get('max_retries', 3))
        else:  # POST
            data = request.get_json() or {}
            url = data.get('url')
            use_proxy = data.get('use_proxy', False)
            max_retries = data.get('max_retries', 3)

        # 参数验证
        if not url:
            return jsonify({
                "success": False,
                "error": "缺少必填参数：url"
            }), 400

        # 创建微信提取器实例
        extractor = WeChatExtractor(use_proxy=use_proxy)

        # 验证是否为微信链接
        if not extractor.is_wechat_url(url):
            return jsonify({
                "success": False,
                "error": "提供的URL不是有效的微信链接"
            }), 400

        # 执行异步提取
        results = asyncio.run(extractor.async_search(url, max_retries=max_retries))

        return jsonify({
            "success": True,
            "results": results,
            "total_count": len(results) if results else 0,
            "url_type": "wechat",
            "timestamp": datetime.now().isoformat()
        })

    except ValueError as e:
        return jsonify({
            "success": False,
            "error": f"参数错误：{str(e)}"
        }), 400
    except Exception as e:
        # 记录详细错误信息
        error_details = traceback.format_exc()
        print(f"微信内容提取接口错误：{error_details}")

        return jsonify({
            "success": False,
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@wx_extractor_bp.route('/batch-extract', methods=['POST'])
def batch_extract_wechat_content():
    """
    批量微信内容提取接口

    支持批量处理多个微信URL的内容提取请求

    Args:
        urls (list): 微信URL列表（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）
        concurrent_limit (int): 并发限制（可选，默认2，微信限制较严）

    Returns:
        JSON: 包含批量提取结果的响应
    """
    try:
        data = request.get_json() or {}
        urls = data.get('urls', [])
        use_proxy = data.get('use_proxy', False)
        max_retries = data.get('max_retries', 3)
        concurrent_limit = data.get('concurrent_limit', 2)  # 微信限制更严格

        # 参数验证
        if not urls or not isinstance(urls, list):
            return jsonify({
                "success": False,
                "error": "缺少必填参数：urls（必须是列表格式）"
            }), 400

        if len(urls) > 50:  # 微信批量处理限制更严格
            return jsonify({
                "success": False,
                "error": "批量处理微信URL数量不能超过50个"
            }), 400

        # 创建微信提取器实例
        extractor = WeChatExtractor(use_proxy=use_proxy)

        # 过滤有效的微信链接
        valid_urls = [url for url in urls if extractor.is_wechat_url(url)]
        invalid_urls = [url for url in urls if not extractor.is_wechat_url(url)]

        if invalid_urls:
            print(f"发现 {len(invalid_urls)} 个无效的微信链接")

        # 批量处理结果
        successful_count = 0
        failed_count = 0

        # 使用信号量控制并发
        async def process_urls():
            nonlocal successful_count, failed_count
            semaphore = asyncio.Semaphore(concurrent_limit)

            async def process_single_url(url):
                nonlocal successful_count, failed_count
                async with semaphore:
                    try:
                        result = await extractor.async_search(url, max_retries=max_retries)
                        if result:
                            successful_count += 1
                            return {"url": url, "success": True, "data": result}
                        else:
                            failed_count += 1
                            return {"url": url, "success": False, "error": "未获取到数据"}
                    except Exception as e:
                        failed_count += 1
                        return {"url": url, "success": False, "error": str(e)}

            tasks = [process_single_url(url) for url in valid_urls]
            return await asyncio.gather(*tasks)

        # 执行批量提取
        batch_results = asyncio.run(process_urls()) if valid_urls else []

        # 添加无效URL的结果
        for invalid_url in invalid_urls:
            batch_results.append({
                "url": invalid_url,
                "success": False,
                "error": "不是有效的微信链接"
            })
            failed_count += 1

        return jsonify({
            "success": True,
            "results": batch_results,
            "total_processed": len(urls),
            "valid_urls": len(valid_urls),
            "invalid_urls": len(invalid_urls),
            "successful_count": successful_count,
            "failed_count": failed_count,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        error_details = traceback.format_exc()
        print(f"批量微信内容提取接口错误：{error_details}")

        return jsonify({
            "success": False,
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@wx_extractor_bp.route('/test', methods=['GET'])
def test_wechat_extractor():
    """
    微信提取器功能测试接口

    用于测试微信提取器的基本功能是否正常

    Returns:
        JSON: 测试结果响应
    """
    try:
        # 测试提取器初始化
        extractor = WeChatExtractor()

        # 测试URL验证功能
        test_urls = [
            "https://mp.weixin.qq.com/s/test123",
            "https://www.baidu.com",
            "https://weixin.qq.com/test"
        ]

        url_test_results = []
        for url in test_urls:
            is_wechat = extractor.is_wechat_url(url)
            url_test_results.append({
                "url": url,
                "is_wechat_url": is_wechat
            })

        return jsonify({
            "success": True,
            "message": "微信提取器功能正常",
            "test_results": {
                "extractor_initialized": True,
                "url_validation_tests": url_test_results,
                "available_methods": [
                    "async_search",
                    "is_wechat_url",
                    "extract_search_results"
                ]
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"测试失败：{str(e)}"
        }), 500


# 错误处理器
@wx_extractor_bp.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "error": "接口不存在",
        "message": "请检查URL路径是否正确",
        "available_endpoints": [
            "/wx/extract",
            "/wx/batch-extract",
            "/wx/test"
        ]
    }), 404


@wx_extractor_bp.errorhandler(405)
def method_not_allowed_error(error):
    """405错误处理"""
    return jsonify({
        "success": False,
        "error": "请求方法不被允许",
        "message": "请检查HTTP方法是否正确"
    }), 405


@wx_extractor_bp.errorhandler(500)
def internal_server_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "error": "服务器内部错误",
        "message": "请稍后重试或联系管理员"
    }), 500