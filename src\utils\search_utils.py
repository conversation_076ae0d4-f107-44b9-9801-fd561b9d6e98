# -*- coding: utf-8 -*-
"""
搜索引擎工具模块
异步搜索引擎工具，支持搜狗微信搜索和百度资讯搜索。

使用示例:
    import asyncio
    from src.utils.search_utils import SogouWeixinSearcher, BaiduNewsSearcher

    # 搜狗微信异步搜索
    sogou_searcher = SogouWeixinSearcher()
    results = await sogou_searcher.async_search("东南大学", page=1)

    # 百度资讯异步搜索
    baidu_searcher = BaiduNewsSearcher()
    results = await baidu_searcher.async_search("东南大学")

返回数据格式:
    每个搜索结果包含以下字段:
    - title: 文章标题
    - summary: 文章摘要/简介
    - url: 文章链接
    - source: 来源网站/公众号名称
    - publish_time: 发布时间
    - search_source: 搜索引擎来源（"搜狗微信"或"百度资讯"）
"""

import aiohttp
import asyncio
from lxml import etree
from urllib.parse import quote, urljoin
import random
from typing import List, Dict, Optional
from abc import ABC, abstractmethod
import datetime
import re

from .time_utils import TimeUtils



class BaseSearcher(ABC):
    """搜索器基类"""

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        """
        初始化搜索器

        Args:
            use_proxy (bool): 是否使用代理
            proxy_config (dict): 代理配置
        """
        self.use_proxy = use_proxy
        self.proxy_config = proxy_config
        self.headers = self._get_headers()
        self.time_utils = TimeUtils()


    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
        }

    @abstractmethod
    def build_search_url(self, query: str, **kwargs) -> str:
        """构建搜索URL"""
        pass

    @abstractmethod
    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        """从HTML内容中提取搜索结果"""
        pass

    async def _make_async_request(self, url: str, max_retries: int = 3, proxy_operator=None) -> Optional[str]:
        """发送异步HTTP请求"""
        for attempt in range(max_retries):
            try:
                await asyncio.sleep(random.uniform(1, 3))

                async with aiohttp.ClientSession() as session:
                    request_kwargs = {
                        'url': url,
                        'headers': self.headers,
                        'timeout': aiohttp.ClientTimeout(total=10)
                    }

                    # 添加代理配置
                    if self.use_proxy and proxy_operator:
                        request_kwargs['proxy'] = proxy_operator.proxy['http']
                    elif self.use_proxy and self.proxy_config:
                        request_kwargs['proxy'] = self.proxy_config.get('http')

                    async with session.get(**request_kwargs) as response:
                        if response.status == 200 and 'captcha' not in str(response.url):
                            return await response.text()
                        else:
                            print(f"请求失败或遇到验证码，尝试重试 ({attempt + 1}/{max_retries})")
                            await asyncio.sleep(random.uniform(5, 10))

            except asyncio.TimeoutError:
                print(f"请求超时，尝试重试 ({attempt + 1}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            except aiohttp.ClientHttpProxyError:
                if proxy_operator:
                    proxy_operator.check_proxy_pool()
                print(f"代理错误，尝试重试 ({attempt + 1}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            except Exception as e:
                print(f"异步请求过程中出现错误: {str(e)}")
                await asyncio.sleep(random.uniform(2, 5))

        print(f"异步搜索失败，已重试 {max_retries} 次")
        return None

    async def async_search(self, query: str, max_retries: int = 3, proxy_operator=None, **kwargs) -> List[Dict[str, str]]:
        """
        执行异步搜索

        Args:
            query (str): 搜索关键词
            max_retries (int): 最大重试次数
            proxy_operator: 代理操作对象
            **kwargs: 其他参数

        Returns:
            List[Dict[str, str]]: 搜索结果列表
        """
        search_url = self.build_search_url(query, **kwargs)
        html_content = await self._make_async_request(search_url, max_retries, proxy_operator)

        if html_content:
            results = self.extract_search_results(html_content)
            return results if results else []
        
        return []


class SogouWeixinSearcher(BaseSearcher):
    """搜狗微信搜索器"""

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        super().__init__(use_proxy, proxy_config)
    
    def build_search_url(self, query: str, **kwargs) -> str:
        page = kwargs.get('page', 1)

        params = {
            'query': query,
            '_sug_type_': '',
            's_from': 'input',
            '_sug_': 'n',
            'type': 2,
            'page': page,
            'ie': 'utf8'
        }

        param_str = '&'.join([f"{k}={quote(str(v), safe='')}" for k, v in params.items()])
        return f"https://weixin.sogou.com/weixin?{param_str}"
    
    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        results = []

        try:
            html = etree.HTML(html_content)
            result_items = html.xpath('//li[.//a[contains(@href, "/link")]]')

            for item in result_items:
                try:
                    title_elements = item.xpath('.//h3/a[contains(@href, "/link")]')
                    if not title_elements:
                        continue

                    title_element = title_elements[0]
                    title = title_element.xpath('string(.)').strip()

                    # 清理标题
                    title = re.sub(r'<[^>]+>', '', title)
                    title = title.replace('_', '')

                    target_url = title_element.get('href', '')
                    if target_url and not target_url.startswith('http'):
                        target_url = urljoin("https://weixin.sogou.com/weixin", target_url)

                    # 提取摘要
                    all_text = item.xpath('string(.)').strip()
                    lines = [line.strip() for line in all_text.split('\n') if line.strip()]

                    summary = ''
                    for line in lines[1:]:
                        if len(line) > 20 and not line.startswith('document.write'):
                            summary = line
                            break

                    # 提取公众号名称
                    author = ''
                    for line in reversed(lines):
                        if not line.startswith('document.write') and len(line) < 50 and len(line) > 2:
                            clean_line = re.sub(r'document\.write\(timeConvert\(\'\d+\'\)\)', '', line).strip()
                            if clean_line and len(clean_line) > 2:
                                author = clean_line
                                break

                    # 提取发布时间
                    publish_time = ''
                    time_pattern = r"document\.write\(timeConvert\('(\d+)'\)\)"
                    time_matches = re.findall(time_pattern, all_text)
                    if time_matches:

                        try:
                            timestamp = int(time_matches[0])
                            publish_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                            publish_time = self.time_utils.parse_time(publish_time)
                        except:
                            publish_time = time_matches[0]

                    if title and target_url:
                        result = {
                            'title': title,
                            'summary': summary,
                            'url': target_url,
                            'source': author,
                            'publish_time': publish_time,
                            'search_source': '搜狗微信'
                        }
                        results.append(result)

                except Exception as e:
                    print(f"提取单个搜索结果时出错: {str(e)}")
                    continue

        except Exception as e:
            print(f"解析搜索结果HTML时出错: {str(e)}")

        return results


class BaiduNewsSearcher(BaseSearcher):
    """百度资讯搜索器"""

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        super().__init__(use_proxy, proxy_config)

    def build_search_url(self, query: str, **kwargs) -> str:
        page = kwargs.get('page', 1)
        keyword_coding = quote(query, "utf-8")
        search_url = f'https://www.baidu.com/s?wd={keyword_coding}&ie=utf-8&medium=0&rtt=4&bsst=1&rsv_dl=news_t_sk&cl=2&tn=news&rsv_bp=1&oq=&rsv_btype=t&f=8&rn=50&pn={page}'
        
        # 更新请求头中的Referer
        self.headers['Referer'] = 'https://www.baidu.com/s?rtt=1&bsst=1&cl=2&tn=news&fr=zhidao&word=' + quote(query, "utf-8")
        
        return search_url

    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        results = []

        try:
            html = etree.HTML(html_content)

            # 检查是否有搜索结果
            no_result = html.xpath('//div[@id="noresult"]/span[@class="noresult-tip"]')
            if no_result:
                return results

            result_items = html.xpath('//div[@class="result-op c-container xpath-log new-pmd"]')

            for result in result_items:
                try:
                    title_elements = result.xpath('.//h3/a')
                    if not title_elements:
                        continue

                    title_element = title_elements[0]
                    title = title_element.xpath('string(.)').strip()
                    href = title_element.get('href', '')

                    # 提取摘要
                    summary_elements = result.xpath('.//span[@class="c-font-normal c-color-text"]')
                    summary = summary_elements[0].xpath('string(.)').strip() if summary_elements else ''

                    # 提取来源
                    source_nodes = result.xpath('.//span[contains(@aria-label,"新闻来源")]/text()')
                    source = source_nodes[0] if source_nodes else ''

                    # 提取发布时间
                    date_nodes = result.xpath('.//span[contains(@aria-label,"发布于")]/text()')
                    raw_time = date_nodes[0] if date_nodes else ''
                    publish_time = self.time_utils.parse_time(raw_time) if raw_time else ''

                    if title and href:
                        result_data = {
                            'title': title,
                            'summary': summary,
                            'url': href,
                            'source': source,
                            'publish_time': publish_time,
                            'search_source': '百度资讯'
                        }
                        results.append(result_data)

                except Exception as e:
                    print(f"提取单个搜索结果时出错: {str(e)}")
                    continue

        except Exception as e:
            print(f"解析搜索结果HTML时出错: {str(e)}")

        return results


# 示例用法
if __name__ == "__main__":
    async def main():
        # 搜狗微信搜索示例
        print("=== 搜狗微信搜索示例 ===")
        sogou_searcher = SogouWeixinSearcher()
        sogou_results = await sogou_searcher.async_search("哇哈哈集团", page=1)

        print(f"搜狗微信搜索结果数量: {len(sogou_results)}")
        for i, result in enumerate(sogou_results[:2], 1):
            print(f"\n结果 {i}:")
            print(f"标题: {result['title']}")
            print(f"简介: {result['summary'][:80]}...")
            print(f"链接: {result['url'][:80]}...")
            print(f"来源: {result['source']}")
            print(f"发布时间: {result['publish_time']}")
            print("-" * 50)

        # 百度资讯搜索示例
        print("\n=== 百度资讯搜索示例 ===")
        baidu_searcher = BaiduNewsSearcher()
        baidu_results = await baidu_searcher.async_search("哇哈哈集团", page=1)

        print(f"百度资讯搜索结果数量: {len(baidu_results)}")
        for i, result in enumerate(baidu_results[:2], 1):
            print(f"\n结果 {i}:")
            print(f"标题: {result['title']}")
            print(f"简介: {result['summary'][:80]}...")
            print(f"链接: {result['url'][:80]}...")
            print(f"来源: {result['source']}")
            print(f"发布时间: {result['publish_time']}")
            print("-" * 50)

    asyncio.run(main())


