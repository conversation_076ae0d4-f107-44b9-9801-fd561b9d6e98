# -*- coding: utf-8 -*-
"""
浏览器工具模块

本模块提供与浏览器相关的工具类，包括获取已登录状态的Chrome浏览器实例和从浏览器获取已登录状态的requests会话。
"""

import time
import requests
import warnings
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

# 抑制所有警告
warnings.filterwarnings('ignore')

CHROMEDRIVER_PATH = r"C:\Users\<USER>\Desktop\chromedriver-win64\chromedriver.exe"
CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

class LoginBrowser():
    """
    用于获取已登录状态的Chrome浏览器实例，支持远程调试模式
    """
    def __init__(self, ip="127.0.0.1", port=9222):
        """
        初始化浏览器配置并启动Chrome浏览器
        
        Args:
            ip (str): 远程调试地址，默认为127.0.0.1
            port (int): 远程调试端口，默认为9222
        """
        self.chrome_driver_service = Service(CHROMEDRIVER_PATH)  # 使用Service对象
        self.load_wait_short = 2
        self.chrome_path = CHROME_PATH
        self.debug_ip = ip
        self.debug_port = port
        # 启动Chrome浏览器并开启远程调试端口
        import subprocess
        try:
            # 检查是否已经有Chrome实例在运行远程调试
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = s.connect_ex((self.debug_ip, self.debug_port))
            s.close()
            if result != 0:  # 端口未被占用，需要启动Chrome
                subprocess.Popen(f'"{self.chrome_path}" --remote-debugging-port={self.debug_port} --user-data-dir="C:\\temp\\chrome_debug_profile"', shell=True)
                print(f"已启动Chrome浏览器并开启远程调试端口{self.debug_port}")
                time.sleep(3)  # 等待浏览器启动
            else:
                print(f"Chrome浏览器已在运行，远程调试端口{self.debug_port}已开启")
        except Exception as e:
            print(f"启动Chrome浏览器时出错: {e}")
        
    def getChromeBrowser(self):
        """
        获取已配置好的Chrome浏览器实例
        
        Returns:
            browser: 配置好的Chrome浏览器实例，已连接到远程调试端口
        """
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"{self.debug_ip}:{self.debug_port}")
        browser = webdriver.Chrome(service=self.chrome_driver_service, options=chrome_options)
        return browser

    def openBrowserWebsite(self, url):
        """
        打开指定URL并处理可能的登录页面
        
        Args:
            url (str): 需要打开的网页URL
            
        Returns:
            browser: 已打开指定URL的浏览器实例，如果遇到登录页面会等待用户手动登录
        """
        browser = self.getChromeBrowser()
        browser.get(url)
        browser.implicitly_wait(self.load_wait_short)
        # 检查是否是登录页面
        keywords = ["login", "auth", "signin"]
        if any(keyword in browser.current_url.lower() for keyword in keywords):
            print("检测到登录页面，请手动登录账号...")
            # 等待用户登录
            login_wait_time = 0
            max_wait_time = 300  # 最长等待5分钟
            while "login" in browser.current_url and login_wait_time < max_wait_time:
                print(f"等待用户登录中...已等待{login_wait_time}秒")
                time.sleep(10)
                login_wait_time += 10

            if "login" in browser.current_url:
                print("等待登录超时，请重新运行程序")
            else:
                print("检测到用户已成功登录")
                time.sleep(3)  # 等待页面加载完成

        return browser



class IndependentBrowser():
    """独立的Chrome浏览器实例，不依赖远程调试模式"""
    def __init__(self):
        pass


    @staticmethod
    def create_independent_browser(headless=False):
        """
        创建独立的Chrome浏览器实例，不依赖远程调试模式

        Args:
            headless (bool): 是否启用无头模式，默认为False
            disable_images (bool): 是否禁用图片加载，默认为True
            disable_javascript (bool): 是否禁用JavaScript，默认为False

        Returns:
            webdriver.Chrome: 独立的Chrome浏览器实例
        """

        chrome_options = Options()  # 创建Chrome选项对象
        # 基本配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')


        # 设置环境变量来抑制ABSL日志
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 添加更多日志抑制参数
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-infobars')

        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

        # 设置窗口大小
        chrome_options.add_argument('--window-size=960,960')

        # 无头模式
        if headless:
            chrome_options.add_argument('--headless')

        # 创建Service对象，并设置日志级别
        service = Service(
            CHROMEDRIVER_PATH,
            log_level=3,  # 减少ChromeDriver日志输出
        )

        # 创建驱动实例
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # 设置隐式等待
        driver.implicitly_wait(10)

        # 设置浏览器属性，防止被检测
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
        })

        return driver


class GetSession():
    """
    用于从浏览器获取已登录状态的requests会话
    """
    def __init__(self):
        """
        初始化会话获取器
        """
        pass

    def get(self, browser):
        """
        从浏览器获取已登录状态的requests会话
        
        Args:
            browser: Selenium浏览器实例，已处于登录状态
            
        Returns:
            session: 包含浏览器cookies的requests会话对象，可用于发送已认证的HTTP请求
        """
        # 获取登录过的session
        sel_cookies = browser.get_cookies()  # 获取selenium侧的cookies
        jar = requests.cookies.RequestsCookieJar()  # 先构建RequestsCookieJar对象
        for i in sel_cookies:
            # 将selenium侧获取的完整cookies的每一个cookie名称和值传入RequestsCookieJar对象
            # domain和path为可选参数，主要是当出现同名不同作用域的cookie时，为了防止后面同名的cookie将前者覆盖而添加的
            jar.set(i['name'], i['value'], domain=i['domain'], path=i['path'])
        session = requests.session()  # requests以session会话形式访问网站
        session.cookies.update(jar)  # 将配置好的RequestsCookieJar对象加入到requests形式的session会话中
        requests.packages.urllib3.disable_warnings()
       
        return session
