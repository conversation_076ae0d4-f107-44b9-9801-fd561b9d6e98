# -*- coding: utf-8 -*-
"""
网页内容提取工具模块

本模块提供网页内容提取相关的功能，包括：
- 通用网页正文提取
- 特殊网站内容处理
- HTML内容解析和清理
"""

import re
import sys
import os
import traceback
import textwrap
from typing import Tuple, Optional
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from gne import GeneralNewsExtractor


def extract_content(html, actual_url):
    """
    提取网页内容
    
    Args:
        html (str): 网页HTML内容
        actual_url (str): 实际访问的URL
        
    Returns:
        tuple: (提取的文章内容, 实际URL)
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')
        
        # 特殊网站处理
        if 'cfi.net.cn' in actual_url:  # 应对中财网的检测的单独处理
            # element = self.driver.find_element(By.XPATH, "//input[@type='submit']")
            # element.click()
            # self.driver.implicitly_wait(5)
            # html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            
        elif "baijiahao.baidu" in actual_url:  # 对百家号进行单独处理
            if soup.find('div', attrs={'data-testid': 'article'}):
                article = soup.find('div', attrs={'data-testid': 'article'}).text
                article = textwrap.fill(article, width=80)  # 每行最多80个字符
                return article, actual_url
            else:
                return "", actual_url
                
        elif 'finance.eastmoney' in actual_url:  # 东方财富网
            html = re.sub(r'<!--\s*EM_StockImg_Start\s*-->.*?<!--\s*EM_StockImg_End\s*-->', '', html)  # 删除股票部分
            soup = BeautifulSoup(html, 'html.parser')
            content_div = soup.find('div', class_='txtinfos')
            if content_div:
                paragraphs = [p.get_text(strip=True) for p in content_div.find_all('p') if p.get_text(strip=True)]
                article = '\n'.join(paragraphs)
                return article, actual_url
            else:
                return "", actual_url
                
        elif 'stockstar' in actual_url:  # 证券之星
            article_div = soup.find('div', class_='article_content')
            if article_div:
                article = article_div.get_text(separator='\n', strip=True)
                return article, actual_url
            else:
                return "", actual_url
        
        # 通用网站处理
        else:
            # 使用GNE提取正文
            extractor = GeneralNewsExtractor()
            result = extractor.extract(html, noise_node_list=[
                '/html/body/div[contains(@class,"statement")]',
                '//*[@id="footer"]',
                '//*[@id="gubaComment"]',
                '//*[@id="reply_editor"]',
                '//*[@id="replyList"]',
                '//div[contains(@class,"zwothers")]'  # zwothers：东方财富网声明
            ])
            article = result.get('content', '')
            if article:
                article = textwrap.fill(article, width=80)
                return article, actual_url
            else:
                # 如果GNE提取失败，尝试使用BeautifulSoup提取
                text = soup.getText(strip=True)
                text = textwrap.fill(text, width=80)
                return text, actual_url
            
    except Exception as e:
        traceback.print_exc()
        return "", actual_url  # 如果出错，返回原始URL
