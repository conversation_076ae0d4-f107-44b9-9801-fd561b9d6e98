import aiohttp
import asyncio
from typing import Dict, Any

from .llm_utils import extract_json



class DifyClient:
    """
    用于连接 Dify API 的异步客户端，支持工作流调用和聊天功能。
    """
    def __init__(self, api_base_url: str, api_key: str):
        """
        初始化 Dify 客户端

        Args:
            api_base_url (str): Dify API 基础URL，例如 "http://localhost/v1"
            api_key (str): Dify API 密钥
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    async def chat(self, messages, model="gpt-3.5-turbo"):
        """
        向 Dify 的 chat 接口发送消息。
        Args：
            messages (list): 聊天消息列表，格式同 OpenAI API。
            model (str): 使用的模型名称。
        Returns:
            dict: Dify 返回的响应内容。
        """
        url = f"{self.api_base_url}/v1/chat/completions"
        payload = {
            "model": model,
            "messages": messages
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=self.headers, json=payload) as response:
                response.raise_for_status()
                return await response.json()

    async def embeddings(self, input_text, model="text-embedding-ada-002"):
        """
        获取文本的 embedding。
        Args:
            input_text (str): 需要嵌入的文本。
            model (str): 嵌入模型名称。
        Returns:
            dict: Dify 返回的响应内容。
        """
        url = f"{self.api_base_url}/v1/embeddings"
        payload = {
            "model": model,
            "input": input_text
        }
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=self.headers, json=payload) as response:
                response.raise_for_status()
                return await response.json()

    async def run_workflow(self, inputs: Dict[str, Any], user: str = "user",
                          response_mode: str = "blocking", max_retries: int = 3,
                          retry_delay: int = 2) -> Dict[str, Any]:
        """
        运行 Dify 工作流，带重试机制

        Args:
            inputs (Dict[str, Any]): 工作流输入参数，格式为 {"变量名": "值"}
            user (str): 用户标识，默认为 "user"
            response_mode (str): 响应模式，"blocking" 或 "streaming"，默认为 "blocking"
            max_retries (int): 最大重试次数，默认为3
            retry_delay (int): 重试间隔秒数，默认为2

        Returns:
            Dict[str, Any]: 工作流执行结果

        Raises:
            aiohttp.ClientError: 当API调用失败时
        """
        url = f"{self.api_base_url}/workflows/run"
        payload = {
            "inputs": inputs,
            "response_mode": response_mode,
            "user": user
        }

        last_exception = None

        for attempt in range(max_retries):
            try:
                timeout = aiohttp.ClientTimeout(total=60)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(url, headers=self.headers, json=payload) as response:
                        response.raise_for_status()
                        result = await response.json()
                        return result

            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                last_exception = e
                if attempt < max_retries - 1:
                    print(f"Dify工作流调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    print(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    print(f"Dify工作流调用最终失败，已重试 {max_retries} 次")
                    break

        # 如果所有重试都失败，抛出最后一个异常
        raise aiohttp.ClientError(f"工作流调用失败: {str(last_exception)}")


class DifyEnterpriseAnalyzer:
    """
    基于 Dify 工作流的文本分析器，专门用于企业动态文本的标签分类和情感分析
    """

    def __init__(self, api_base_url: str, api_key: str):
        """
        初始化文本分析器

        Args:
            api_base_url (str): Dify API 基础URL
            api_key (str): Dify API 密钥
        """
        self.client = DifyClient(api_base_url, api_key)

        # 预定义标签列表
        self.predefined_tags = [
            "公益捐赠", "奖教助学", "高校合作", "营收年报", "荣誉成就","股票分析","公司变动",
            "获奖", "入选", "收录", "成果", "专利", "消息", "无关", "其他"
        ]


    async def analyze_enterprise_content(self, content: str, enterprise: str, title: str = "", 
                                       user: str = "enterprise_analyzer",
                                       max_retries: int = 3, retry_delay: int = 2) -> Dict[str, Any]:
        """
        分析企业动态内容，返回标签分类和情感分析结果，带重试机制

        Args:
            content (str): 企业动态文本内容
            enterprise (str): 企业名称
            title (str): 文章标题（可选）
            user (str): 用户标识
            max_retries (int): 最大重试次数，默认为3
            retry_delay (int): 重试间隔秒数，默认为2

        Returns:
            Dict[str, Any]: 分析结果，包含以下字段：
            - success (bool): 分析是否成功
            - classification (Dict): 分类结果，包含 tags 列表
            - sentiment (Dict): 情感分析结果
            - error (str): 错误信息（如果失败）
        """
        if not content or not content.strip():
            return {
                "success": False,
                "error": "输入内容为空",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0}
            }

        # 组合标题和内容
        full_text = f"目标企业：{enterprise}\n动态文本：{title}\n{content}" if title else f"目标企业：{enterprise}\n动态文本：{content}"

        # 直接调用 Dify 工作流进行分析，使用重试机制
        try:
            inputs = {"text": full_text}
            workflow_result = await self.client.run_workflow(inputs, user, "blocking", max_retries, retry_delay)

            # 提取工作流输出结果
            analysis_result = None
            if "data" in workflow_result and "outputs" in workflow_result["data"]:
                output_text = workflow_result["data"]["outputs"].get("text", "")
                if output_text:
                    analysis_result = extract_json(output_text)
                    
                    # 如果提取失败但有raw_output，尝试进一步处理
                    if "raw_output" in analysis_result and analysis_result.get("raw_output"):
                        print(f"JSON提取失败，使用默认结构")
                        analysis_result = {
                            "classification": {"tags": []},
                            "sentiment": {"polarity": "Neutral", "intensity": 0},
                            "raw_output": analysis_result["raw_output"]
                        }

            if analysis_result is None:
                return {
                    "success": False,
                    "error": "工作流输出为空或格式错误",
                    "classification": {"tags": []},
                    "sentiment": {"polarity": "Neutral", "intensity": 0}
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Dify 工作流调用失败: {str(e)}",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0}
            }

        # 处理分析结果
        try:    
            # 提取分类结果
            classification = analysis_result.get("classification", {})
            tags = classification.get("tags", [])

            # 验证标签是否在预定义列表中
            valid_tags = [tag for tag in tags if tag in self.predefined_tags]

            # 提取情感分析结果
            sentiment = analysis_result.get("sentiment", {})
            polarity = sentiment.get("polarity", "Neutral")
            intensity = sentiment.get("intensity", 0)
            aspect_analysis = sentiment.get("aspect_analysis", [])

            return {
                "success": True,
                "classification": {
                    "tags": valid_tags,
                    "all_tags": tags  # 保留所有标签用于调试
                },
                "sentiment": {
                    "polarity": polarity,
                    "intensity": float(intensity) if intensity else 0,
                    "aspect_analysis": aspect_analysis
                },
                "raw_result": analysis_result  # 保留原始结果用于调试
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"结果解析失败: {str(e)}",
                "classification": {"tags": []},
                "sentiment": {"polarity": "Neutral", "intensity": 0},
                "raw_result": analysis_result
            }
