import json
import re
from json_repair import repair_json


def extract_json(text: str) -> dict:
    """
    从文本中提取JSON，支持多种格式和场景，使用json-repair进行修复
    
    Args:
        text (str): 包含JSON的文本
    
    Returns:
        dict: 解析后的JSON字典，失败时返回包含原始文本的字典
    """
    if not text or not text.strip():
        return {"raw_output": ""}
    
    # 清理文本
    text = text.strip()
    
    # 场景1: 直接尝试解析整个文本
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass
    
    # 场景2: 使用json-repair修复后解析
    try:
        repaired_json = repair_json(text)
        result = json.loads(repaired_json)
        # 验证是否包含期望的字段
        if isinstance(result, dict) and ('classification' in result or 'sentiment' in result):
            return result
    except Exception as e:
        print(f"json-repair修复失败: {e}")
    
    # 场景3: 移除markdown代码块标记后使用json-repair
    patterns_to_remove = [
        r'^```json\s*',  # 开头的```json
        r'^```\s*',      # 开头的```
        r'```\s*$',      # 结尾的```
        r'^json\s*',     # 开头的json关键字
    ]
    
    cleaned_text = text
    for pattern in patterns_to_remove:
        cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.MULTILINE)
    
    try:
        repaired_json = repair_json(cleaned_text.strip())
        result = json.loads(repaired_json)
        if isinstance(result, dict) and ('classification' in result or 'sentiment' in result):
            return result
    except Exception as e:
        print(f"清理后json-repair修复失败: {e}")
    
    # 场景4: 使用正则表达式提取JSON对象后修复
    json_patterns = [
        r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 简单嵌套JSON
        r'\{.*?\}',  # 非贪婪匹配
        r'\{.*\}',   # 贪婪匹配
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, text, re.DOTALL)
        for match in matches:
            try:
                repaired_json = repair_json(match.strip())
                result = json.loads(repaired_json)
                # 验证是否包含期望的字段
                if isinstance(result, dict) and ('classification' in result or 'sentiment' in result):
                    return result
            except Exception:
                continue
    
    # 场景5: 手动清理换行符后使用json-repair
    try:
        cleaned_text = re.sub(r'\n\s*', '', text)
        repaired_json = repair_json(cleaned_text)
        result = json.loads(repaired_json)
        if isinstance(result, dict):
            return result
    except Exception as e:
        print(f"手动清理后json-repair修复失败: {e}")
    
    # 场景6: 如果所有方法都失败，返回原始文本
    print(f"所有JSON提取方法都失败，原始文本: {text[:200]}...")
    return {"raw_output": text}
