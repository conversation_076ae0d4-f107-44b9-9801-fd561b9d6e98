# -*- coding: utf-8 -*-
"""
微信提取器路由模块

本模块提供微信相关的内容提取功能，调用src/wx_content_extractor.py中的现有函数
包括：
- 微信公众号文章提取
- 微信链接内容解析
- 微信文章批量处理
- 微信内容格式化

主要路由：
- /wx/extract - 微信内容提取接口
- /wx/batch-extract - 批量微信内容提取
- /wx/test - 功能测试接口
"""

import sys
import os
import asyncio
import json
import traceback
from datetime import datetime
from flask import Blueprint, request, jsonify
from typing import List, Dict, Any, Optional
from urllib.parse import quote, urlparse

# 添加src/utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'utils'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'service'))

# 导入搜索器和工具类
from service.search import WeChatContentSearcher

# 创建微信提取器路由蓝图
wechat_extractor_bp = Blueprint('wechat_extractor', __name__, url_prefix='/wx')


@wechat_extractor_bp.route('/extract', methods=['GET', 'POST'])
def extract_wechat_content():
    """
    微信内容提取接口
    
    支持GET和POST请求，用于提取微信公众号文章内容
    调用src/wx_content_extractor.py中的现有函数
    
    Args:
        url (str): 微信文章URL（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）
    
    Returns:
        JSON: 包含提取结果的响应
            - success (bool): 请求是否成功
            - results (list): 提取结果列表
            - error (str): 错误信息（仅在失败时返回）
    """
    try:
        # 获取请求参数
        if request.method == 'GET':
            url = request.args.get('url')
            use_proxy = request.args.get('use_proxy', 'false').lower() == 'true'
            max_retries = int(request.args.get('max_retries', 3))
        else:  # POST
            data = request.get_json() or {}
            url = data.get('url')
            use_proxy = data.get('use_proxy', False)
            max_retries = data.get('max_retries', 3)
        
        # 参数验证
        if not url:
            return jsonify({
                "success": False, 
                "error": "缺少必填参数：url"
            }), 400
        
        # 创建微信内容搜索器实例
        searcher = WeChatContentSearcher(use_proxy=use_proxy)
        
        # 验证是否为微信链接
        if not searcher.is_wechat_url(url):
            return jsonify({
                "success": False,
                "error": "提供的URL不是有效的微信链接"
            }), 400
        
        # 执行异步提取
        results = asyncio.run(searcher.async_search(url, max_retries=max_retries))
        
        # 统计成功数量
        successful_results = [r for r in results if r.get('success', False)]
        
        return jsonify({
            "success": len(successful_results) > 0, 
            "results": results,
            "total_count": len(results),
            "successful_count": len(successful_results),
            "url_type": "wechat",
            "timestamp": datetime.now().isoformat()
        })
        
    except ValueError as e:
        return jsonify({
            "success": False, 
            "error": f"参数错误：{str(e)}"
        }), 400
    except Exception as e:
        # 记录详细错误信息
        error_details = traceback.format_exc()
        print(f"微信内容提取接口错误：{error_details}")
        
        return jsonify({
            "success": False, 
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@wechat_extractor_bp.route('/batch-extract', methods=['POST'])
def batch_extract_wechat_content():
    """
    批量微信内容提取接口
    
    支持批量处理多个微信URL的内容提取请求
    调用src/wx_content_extractor.py中的batch_extract方法
    
    Args:
        urls (list): 微信URL列表（必填）
        use_proxy (bool): 是否使用代理（可选，默认False）
        max_retries (int): 最大重试次数（可选，默认3）
        concurrent_limit (int): 并发限制（可选，默认2，微信限制较严）
        generate_pdf (bool): 是否生成PDF文件（可选，默认True）
    
    Returns:
        JSON: 包含批量提取结果的响应
    """
    try:
        data = request.get_json() or {}
        urls = data.get('urls', [])
        use_proxy = data.get('use_proxy', False)
        max_retries = data.get('max_retries', 3)
        concurrent_limit = data.get('concurrent_limit', 2)  # 微信限制更严格
        generate_pdf = data.get('generate_pdf', True)
        
        # 参数验证
        if not urls or not isinstance(urls, list):
            return jsonify({
                "success": False,
                "error": "缺少必填参数：urls（必须是列表格式）"
            }), 400
        
        if len(urls) > 50:  # 微信批量处理限制更严格
            return jsonify({
                "success": False,
                "error": "批量处理微信URL数量不能超过50个"
            }), 400
        
        # 创建微信文章提取器实例（直接使用src中的类）
        try:
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
            from wx_content_extractor import WechatArticleExtractor
            
            extractor = WechatArticleExtractor(
                use_proxy=use_proxy,
                thread_count=min(concurrent_limit, len(urls))
            )
            
            # 调用批量提取方法
            batch_results = extractor.batch_extract(
                url_list=urls,
                max_retries=max_retries,
                concurrent_limit=concurrent_limit,
                generate_pdf=generate_pdf
            )
            
            # 统计结果
            successful_count = sum(1 for r in batch_results if r.get('success', False))
            failed_count = len(batch_results) - successful_count
            
            # 过滤有效的微信链接
            valid_urls = [url for url in urls if extractor.is_wechat_url(url)]
            invalid_urls = [url for url in urls if not extractor.is_wechat_url(url)]
            
            return jsonify({
                "success": True,
                "results": batch_results,
                "total_processed": len(urls),
                "valid_urls": len(valid_urls),
                "invalid_urls": len(invalid_urls),
                "successful_count": successful_count,
                "failed_count": failed_count,
                "timestamp": datetime.now().isoformat()
            })
            
        except ImportError as e:
            return jsonify({
                "success": False,
                "error": f"微信提取器模块导入失败：{str(e)}"
            }), 500
        finally:
            # 确保关闭提取器资源
            try:
                if 'extractor' in locals():
                    extractor.close()
            except:
                pass
        
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"批量微信内容提取接口错误：{error_details}")
        
        return jsonify({
            "success": False,
            "error": f"服务器内部错误：{str(e)}"
        }), 500


@wechat_extractor_bp.route('/test', methods=['GET'])
def test_wechat_extractor():
    """
    微信提取器功能测试接口
    
    用于测试微信提取器的基本功能是否正常
    
    Returns:
        JSON: 测试结果响应
    """
    try:
        # 测试微信内容搜索器初始化
        searcher = WeChatContentSearcher()
        
        # 测试URL验证功能
        test_urls = [
            "https://mp.weixin.qq.com/s/test123",
            "https://www.baidu.com",
            "https://weixin.qq.com/test"
        ]
        
        url_test_results = []
        for url in test_urls:
            is_wechat = searcher.is_wechat_url(url)
            url_test_results.append({
                "url": url,
                "is_wechat_url": is_wechat
            })
        
        return jsonify({
            "success": True,
            "message": "微信提取器功能正常",
            "test_results": {
                "searcher_initialized": True,
                "url_validation_tests": url_test_results,
                "available_methods": [
                    "async_search",
                    "is_wechat_url",
                    "extract_search_results"
                ]
            },
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"测试失败：{str(e)}"
        }), 500


# 错误处理器
@wechat_extractor_bp.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    return jsonify({
        "success": False,
        "error": "接口不存在",
        "message": "请检查URL路径是否正确",
        "available_endpoints": [
            "/wx/extract",
            "/wx/batch-extract", 
            "/wx/test"
        ]
    }), 404


@wechat_extractor_bp.errorhandler(405)
def method_not_allowed_error(error):
    """405错误处理"""
    return jsonify({
        "success": False,
        "error": "请求方法不被允许",
        "message": "请检查HTTP方法是否正确"
    }), 405


@wechat_extractor_bp.errorhandler(500)
def internal_server_error(error):
    """500错误处理"""
    return jsonify({
        "success": False,
        "error": "服务器内部错误",
        "message": "请稍后重试或联系管理员"
    }), 500
