# 企业动态采集系统

[![Python Version](https://img.shields.io/badge/python-3.12+-blue.svg)](https://python.org)
[![uv](https://img.shields.io/badge/package%20manager-uv-orange.svg)](https://github.com/astral-sh/uv)

## 项目概述

企业动态采集系统是一个基于Python的综合性新闻动态采集工具，专门用于自动化采集企业新闻和校友动态信息。系统通过多种搜索引擎（主要是百度资讯）获取相关新闻报道，并将这些信息进行结构化处理、内容提取、智能分析，最终保存到数据库或Excel文件中。

### 核心特性

- 🚀 **高性能异步采集**：基于asyncio和aiohttp实现高效并发爬取
- 🔍 **多搜索引擎支持**：集成百度资讯、搜狗微信等搜索引擎
- 🛡️ **智能代理管理**：集成阿布云代理池，支持自动续费和状态检查
- 📄 **全文内容提取**：支持从新闻链接提取完整文章内容并转换为PDF
- 🤖 **AI智能分析**：集成Dify AI工作流，自动生成新闻标签和情感分析
- 💾 **多种存储方式**：支持Excel文件和MongoDB数据库存储
- 🔧 **模块化设计**：高度模块化的代码结构，易于扩展和维护

## 主要功能

### 🎯 核心采集功能

1. **企业新闻采集**：从百度资讯等搜索引擎获取企业相关新闻动态
2. **校友动态采集**：专门针对高校校友的动态信息采集，支持复杂的学校优先级处理
3. **异步并发处理**：使用现代异步编程技术实现高效的并发爬取
4. **智能代理管理**：集成阿布云代理服务，支持自动续费、状态监控和故障恢复

### 📊 数据处理功能

5. **智能内容过滤**：根据企业名称、发布时间、内容相关性等多维度条件过滤采集结果
6. **网页内容提取**：支持从新闻链接中提取完整的文章内容，兼容多种新闻网站格式
7. **微信公众号处理**：专门的微信公众号文章内容提取器，支持批量处理
8. **PDF文档生成**：自动将网页内容转换为PDF文件并本地存储
9. **多存储支持**：灵活的数据存储方案，支持Excel文件和MongoDB数据库
10. **AI智能标签**：使用本地Dify AI工作流为新闻内容生成分类标签和情感分析

## 📁 项目结构

```
enterprise-dynamic-collection/
├── 📂 src/                              # 源代码目录
│   ├── 🎯 enterprise_news_collector.py # 企业新闻采集器主程序
│   ├── 🎓 alumni_news_collector.py     # 校友动态采集器主程序
│   ├── 🌐 web_content_extractor.py     # 网页内容提取工具
│   ├── 💬 wx_content_extractor.py      # 微信公众号内容提取工具
│   ├── 🔗 daili.py                     # 阿布云代理管理模块
│   ├── 📂 config/                      # 配置文件目录
│   │   └── prompt_config.py            # LLM提示词配置
│   └── 📂 utils/                       # 工具模块目录
│       ├── api_utils.py                # Laungee API工具类
│       ├── browser_utils.py            # 浏览器自动化工具
│       ├── data_utils.py               # 数据处理工具
│       ├── time_utils.py               # 时间处理工具
│       ├── search_utils.py             # 搜索引擎工具（百度资讯、搜狗微信）
│       ├── llm_utils.py                # LLM工具类（JSON解析等）
│       ├── MongoClient.py              # MongoDB客户端
│       ├── dify.py                     # Dify AI服务集成
│       └── description.py              # 描述信息工具
├── 📂 test/                            # 测试目录
│   ├── test_w.py                       # 主要测试脚本
│   └── test_wx.py                      # 微信内容提取测试
├── 📂 temp/                            # 临时文件目录
│   ├── create_mongodb_collection.py    # MongoDB集合创建脚本
│   ├── mongo_manager.py                # MongoDB管理工具
│   └── temp_create_collection.py       # 临时集合创建脚本
├── 📂 log/                             # 日志目录，存放程序运行日志
│   ├── 企业动态采集日志/                # 企业新闻采集日志
│   ├── 复旦大学动态采集日志/            # 校友动态采集日志
│   └── 网页内容爬取日志/                # 网页内容提取日志
├── 📂 output/                          # 输出目录
│   ├── pdf/                            # PDF文件存储目录
│   ├── *.xlsx                          # 采集结果Excel文件
│   └── *.json                          # JSON格式输出文件
├── 📄 requirements.txt                 # Python依赖包列表
├── 📄 pyproject.toml                   # 项目配置文件（uv包管理器）
├── 🔒 uv.lock                          # 依赖锁定文件
└── 📖 readme.md                        # 项目说明文档
```

## 🛠️ 技术栈

### 🐍 核心技术

- **Python 3.12+**：主要开发语言，使用现代Python特性
- **asyncio/aiohttp**：异步HTTP请求和并发处理，提供高性能网络访问
- **pandas**：强大的数据处理和分析库
- **lxml**：高性能HTML/XML解析器

### 💾 数据库与存储

- **pymongo**：MongoDB数据库操作，支持文档型数据存储
- **openpyxl**：Excel文件读写，支持复杂的表格操作

### 🌐 网络与爬虫

- **requests**：同步HTTP请求库，用于API调用
- **selenium**：浏览器自动化框架，用于代理登录和JavaScript渲染页面处理
- **beautifulsoup4**：HTML解析和内容提取，支持多种解析器
- **gne (GeneralNewsExtractor)**：通用新闻提取器，智能提取新闻正文

### 🔐 认证与安全

- **pyjwt**：JWT令牌生成和验证，用于API认证
- **阿布云代理服务**：专业IP代理服务集成，支持动态IP切换

### ⏰ 时间处理

- **ntplib**：网络时间协议客户端，用于时间同步
- **python-dateutil**：强大的日期时间解析和处理库
- **pytz**：时区处理库，支持全球时区转换

### 🤖 AI与智能处理

- **json-repair**：智能JSON修复和解析工具
- **Dify**：本地AI工作流平台集成，支持文本分析和标签生成

### 📦 包管理

- **uv**：现代Python包管理器，比pip更快的依赖解析和安装

## 🚀 快速开始

### 📋 环境要求

- **Python 3.12+**：确保使用Python 3.12或更高版本
- **Chrome浏览器**：用于代理登录和JavaScript页面处理
- **MongoDB**：用于数据存储（可选，也可使用Excel文件）
- **Windows系统**：当前版本主要针对Windows环境优化

### 📦 安装依赖

#### 方法一：使用uv包管理器（推荐）

uv是比pip更快的现代Python包管理器：

```bash
# 安装uv（如果尚未安装）
pip install uv

# 克隆项目
git clone <repository-url>
cd enterprise-dynamic-collection

# 同步依赖（自动创建虚拟环境）
uv sync

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate
```

#### 方法二：使用传统pip

```bash
# 克隆项目
git clone <repository-url>
cd enterprise-dynamic-collection

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 🌐 配置Chrome远程调试

系统使用Chrome浏览器的远程调试功能来管理代理登录和特殊网站处理。

#### 自动启动（推荐）

程序会自动尝试启动Chrome远程调试模式，无需手动配置。

#### 手动启动（备用方案）

如果自动启动失败，可以手动启动：

```bash
# Windows
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug_profile"

# 或者使用相对路径
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug_profile"
```

#### 验证配置

启动后，访问 `http://localhost:9222` 应该能看到Chrome调试界面。

## 📖 使用指南

### 🎯 企业新闻采集

企业新闻采集器是系统的核心功能之一，用于自动采集企业相关的新闻动态。

#### 基本使用

1. **确保环境准备就绪**：

   - Chrome浏览器已安装
   - 虚拟环境已激活
   - 依赖包已安装
2. **运行采集器**：

```bash
# 基本运行（使用默认配置）
python src/enterprise_news_collector.py

# 或者使用uv运行
uv run python src/enterprise_news_collector.py
```

程序会自动执行以下流程：

- 从Laungee API获取企业列表
- 异步并发采集企业新闻
- 数据过滤和清洗
- 保存结果到Excel文件和MongoDB数据库

#### 🔧 命令行参数详解

| 参数                   | 类型 | 默认值 | 说明                            |
| ---------------------- | ---- | ------ | ------------------------------- |
| `--no-proxy`         | 标志 | False  | 不使用阿布云代理池，直接连接    |
| `--limit`            | 整数 | 100    | 限制处理的企业数量，用于测试    |
| `--keep-days`        | 整数 | 365    | 保留多少天内的新闻，过滤旧新闻  |
| `--no-keep-notes`    | 标志 | False  | 不保留Excel中的备注列           |
| `--addition-times`   | 整数 | 1      | 补采次数，总采集次数=1+补采次数 |
| `--concurrent-limit` | 整数 | 5      | 并发请求限制，避免过载          |

#### 💡 使用示例

```bash
# 测试模式：不使用代理，只处理5家企业
python src/enterprise_news_collector.py --no-proxy --limit 5

# 快速采集：保留最近30天的新闻，提高并发
python src/enterprise_news_collector.py --keep-days 30 --concurrent-limit 10

# 完整采集：使用代理，处理所有企业，保留一年内新闻
python src/enterprise_news_collector.py --keep-days 365 --concurrent-limit 5

# 精简输出：不保留备注，减少文件大小
python src/enterprise_news_collector.py --no-keep-notes --limit 50
```

### 🎓 校友动态采集

校友动态采集器专门用于采集高校校友的新闻动态，支持复杂的学校优先级处理逻辑。

#### 基本使用

```bash
# 基本运行（默认采集复旦大学校友）
python src/alumni_news_collector.py

# 使用uv运行
uv run python src/alumni_news_collector.py
```

程序执行流程：

- 从API获取指定学校的校友名单
- 应用学校优先级处理逻辑
- 异步并发采集校友相关动态
- 数据过滤和清洗
- 保存到MongoDB数据库和Excel文件

#### 🔧 命令行参数详解

| 参数                   | 类型   | 默认值     | 说明                         |
| ---------------------- | ------ | ---------- | ---------------------------- |
| `--school`           | 字符串 | '复旦大学' | 目标采集学校名称             |
| `--no-proxy`         | 标志   | False      | 不使用阿布云代理池           |
| `--keep-days`        | 整数   | 300        | 保留多少天内的动态           |
| `--addition-times`   | 整数   | 1          | 补采次数                     |
| `--concurrent-limit` | 整数   | 5          | 并发请求限制                 |
| `--limit`            | 整数   | 5          | 限制采集的校友人数（测试用） |

#### 💡 使用示例

```bash
# 采集清华大学校友动态，不使用代理
python src/alumni_news_collector.py --school 清华大学 --no-proxy

# 快速测试：采集10位校友，保留60天内动态
python src/alumni_news_collector.py --keep-days 60 --limit 10

# 完整采集：采集复旦大学所有校友，保留300天动态
python src/alumni_news_collector.py --school 复旦大学 --keep-days 300

# 高并发采集：提高并发数，加快采集速度
python src/alumni_news_collector.py --concurrent-limit 10 --limit 20
```

### 🌐 网页内容提取

网页内容提取器用于从新闻链接中提取完整的文章内容，并生成PDF文件。

#### 基本使用

```bash
# 企业动态内容提取
python src/web_content_extractor.py --mode enterprise

# 校友动态内容提取
python src/web_content_extractor.py --mode alumni

# 指定输入文件
python src/web_content_extractor.py --mode enterprise --input "output/企业动态采集结果_2025-07-17.xlsx"
```

#### 🔧 命令行参数

| 参数               | 类型   | 默认值       | 说明                        |
| ------------------ | ------ | ------------ | --------------------------- |
| `--mode`         | 字符串 | 'enterprise' | 模式选择：enterprise/alumni |
| `--input`        | 字符串 | 自动检测     | 指定输入Excel文件路径       |
| `--threads`      | 整数   | 4            | 并发线程数量                |
| `--batch-size`   | 整数   | 10           | MongoDB批量保存大小         |
| `--disable-dify` | 标志   | False        | 禁用Dify AI分析             |

#### 功能特性

- ✅ **多网站支持**：兼容百家号、中财网、东方财富等主流新闻网站
- ✅ **智能提取**：使用GNE通用新闻提取器进行智能内容提取
- ✅ **PDF生成**：自动将网页内容转换为PDF文件并本地存储
- ✅ **AI分析**：集成Dify AI工作流，生成内容标签和情感分析
- ✅ **多线程处理**：支持多线程并发处理，提高效率
- ✅ **错误恢复**：智能跳过验证码和登录页面，支持重试机制

### 💬 微信公众号内容提取

专门的微信公众号文章内容提取器，支持批量处理和内容清洗。

#### 编程接口使用

```python
from src.wx_content_extractor import WechatArticleExtractor

# 单篇文章提取
extractor = WechatArticleExtractor()
result = extractor.extract_content("https://mp.weixin.qq.com/s/...")
print(f"标题: {result['title']}")
print(f"内容: {result['content']}")
extractor.close()

# 批量提取
url_list = [
    "https://mp.weixin.qq.com/s/...",
    "https://mp.weixin.qq.com/s/...",
]
results = extractor.batch_extract(url_list)
for result in results:
    print(f"标题: {result['title']}")
    print(f"内容长度: {len(result['content'])}")
```

#### 功能特性

- ✅ **专业解析**：专门针对微信公众号文章结构优化
- ✅ **内容清洗**：自动去除多余空白、特殊符号和表情
- ✅ **批量处理**：支持批量提取多篇文章
- ✅ **结构化输出**：返回标题、内容、URL等结构化数据

## 🔧 核心模块详解

### 🎯 主要采集器

#### EnterpriseNewsCollector（企业新闻采集器）

企业新闻采集器是系统的核心组件，专门负责企业新闻的自动化采集和处理。

**核心功能：**

- 🔗 **API集成**：从Laungee API获取企业列表和基础信息
- 🚀 **异步采集**：使用asyncio和aiohttp实现高效并发爬取
- 🔍 **多源搜索**：主要通过百度资讯搜索引擎获取企业相关新闻
- 🧹 **智能过滤**：基于时间、内容相关性等多维度过滤采集结果
- 💾 **多存储支持**：同时保存到Excel文件和MongoDB数据库
- 🤖 **AI标签生成**：集成Dify AI工作流，自动生成新闻分类标签

**技术特点：**

- 支持代理池管理，避免IP封禁
- 智能重试机制，提高采集成功率
- 详细的日志记录，便于问题排查
- 灵活的配置参数，适应不同采集需求

#### AlumniNewsCollector（校友动态采集器）

专门用于采集高校校友动态信息的采集器，具有复杂的学校优先级处理逻辑。

**核心功能：**

- 🎓 **校友名单获取**：从API获取指定学校的校友名单
- 🔄 **优先级处理**：实现复杂的学校优先级逻辑（已服务高校优先、复旦管院优先等）
- 🔍 **动态搜索**：异步采集校友相关的新闻动态
- 📊 **数据清洗**：专门的校友数据过滤和清洗逻辑
- 💾 **数据存储**：保存到MongoDB数据库和Excel文件

**技术特点：**

- 支持多学校批量采集
- 智能的校友信息匹配算法
- 灵活的时间范围控制
- 详细的采集进度显示

### 🌐 内容提取工具

#### WebContentExtractor（网页内容提取器）

强大的网页内容提取器，用于从新闻链接中提取完整文章内容并生成PDF文件。

**核心功能：**

- 🌐 **多网站支持**：兼容百家号、中财网、东方财富等主流新闻网站
- 🧠 **智能提取**：使用GNE（通用新闻提取器）进行智能内容提取
- 📄 **PDF生成**：自动将网页内容转换为PDF文件并本地存储
- 🤖 **AI分析**：集成Dify AI工作流，生成内容标签和情感分析
- 🔄 **多线程处理**：支持多线程并发处理，大幅提高效率
- 🛡️ **错误恢复**：智能跳过验证码和登录页面，支持重试机制

**技术特点：**

- 使用Chrome浏览器驱动池，提高并发性能
- 支持跳转链接的自动跟踪
- 智能的内容清洗和格式化
- 灵活的批量处理机制

#### WechatArticleExtractor（微信公众号提取器）

专门的微信公众号文章内容提取器，针对微信文章结构进行了优化。

**核心功能：**

- 💬 **专业解析**：专门针对微信公众号文章结构优化
- 🧹 **内容清洗**：自动去除多余空白、特殊符号和表情
- 📦 **批量处理**：支持批量提取多篇文章
- 📊 **结构化输出**：返回标题、内容、URL等结构化数据

**技术特点：**

- 使用BeautifulSoup进行精确的HTML解析
- 智能的文本清洗算法
- 支持代理访问，避免访问限制
- 灵活的驱动管理机制

### 🧰 工具模块

#### LaungeeAPIUtils（蓝矩API工具类）

提供统一的API调用接口，封装了与Laungee数据服务的所有交互。

**核心功能：**

- 🔑 **JWT认证**：自动生成和管理JWT令牌，确保API安全访问
- 🏢 **企业数据**：获取企业列表和详细信息
- 🎓 **校友数据**：获取校友名单，支持复杂的学校优先级处理
- 📤 **数据提交**：提交采集到的新闻数据到API服务
- 🌐 **通用请求**：封装的通用API请求方法，支持各种API操作

**技术特点：**

- 静态方法设计，无需实例化即可使用
- 内置错误处理和重试机制
- 灵活的参数配置，支持自定义API密钥

#### 搜索引擎工具

提供统一的搜索引擎接口，支持多种搜索引擎的异步访问。

**主要组件：**

- 🔍 **BaiduNewsSearcher**：百度资讯异步搜索器，专门用于新闻搜索
- 💬 **SogouWeixinSearcher**：搜狗微信异步搜索器，用于微信公众号内容搜索

**技术特点：**

- 统一的搜索结果格式，便于后续处理
- 异步并发搜索能力，大幅提高效率
- 智能的结果解析和清洗
- 内置的错误处理和重试机制

#### LLM集成工具

提供与大语言模型和AI服务的集成功能。

**主要组件：**

- 🧩 **llm_utils.py**：LLM工具类，包含JSON解析和修复功能
- 🤖 **dify.py**：Dify AI服务集成，支持本地AI工作流
- 📝 **prompt_config.py**：LLM提示词配置管理，存储各种场景的提示模板

**技术特点：**

- 智能的JSON修复功能，处理LLM返回的不规范JSON
- 灵活的提示词管理，支持多种分析场景
- 与本地Dify AI服务的无缝集成

#### 数据处理工具

提供各种数据处理、格式化和存储功能。

**主要组件：**

- 📊 **DataUtils**：数据处理和格式化工具，提供通用数据清洗功能
- ⏰ **TimeUtils**：时间处理和同步工具，支持多种时间格式解析
- 💾 **MongoClient**：MongoDB数据库客户端，封装数据库操作

**技术特点：**

- 强大的时间解析能力，支持各种格式的时间字符串
- 网络时间同步功能，确保时间准确性
- 灵活的数据库操作接口，支持批量处理

#### 浏览器与代理工具

提供浏览器自动化和代理管理功能。

**主要组件：**

- 🌐 **browser_utils.py**：浏览器自动化工具，获取已登录状态的浏览器实例
- 🔗 **ProxyOperator**：阿布云代理管理，支持自动续费和状态检查

**技术特点：**

- 智能的Chrome远程调试功能
- 自动代理续费机制，确保代理服务不中断
- 网络时间同步，准确判断代理过期时间
- 灵活的会话管理，支持获取已登录状态的会话

## ⚙️ 配置说明

### 🔑 API配置

项目依赖Laungee API服务，提供企业和校友数据的获取接口。

#### 基本配置

- **API密钥**：默认使用内置密钥 `GxSRnVWkF4doLBBHqpyMMu8pK8p9KOMv`
- **自定义密钥**：可在 `src/utils/api_utils.py` 中的 `DEFAULT_API_KEY` 修改

#### API端点

| 功能     | 端点                                                | 说明             |
| -------- | --------------------------------------------------- | ---------------- |
| 企业列表 | `https://data.laungee.com/crawapi/getEntNameList` | 获取企业基础信息 |
| 企业新闻 | `https://data.laungee.com/crawapi/insertEntNews`  | 提交企业新闻数据 |
| 校友列表 | `https://data.laungee.com/crawapi/getXyNameList`  | 获取校友名单数据 |

#### JWT认证

系统使用JWT令牌进行API认证，令牌包含以下信息：

- `timestamp`：时间戳（当前时间-5分钟）
- `iss`：发行者标识（LaungeeData）
- `uid`：用户标识（crawlab）

### 🔗 代理配置

集成阿布云代理服务，提供稳定的IP代理支持。

#### 代理账户配置

```python
# 在 src/daili.py 中配置
User_tunnelId = {
    'HJ349K4EU1Y2RQ8D': 97085,
    'H7FU74053136906D': 95840,
    'H8R4347UP2710CSD': 95827,
    'HT99W04OL07C735D': 94298,
}
```

#### 代理控制选项

- **命令行控制**：使用 `--no-proxy` 参数禁用代理
- **代码控制**：在初始化时设置 `use_proxy=False`
- **自动续费**：系统会自动检查代理过期时间并续费
- **余额监控**：自动检查账户余额，余额不足时发出警告

#### 代理类型

- **动态版**：`http-dyn.abuyun.com:9020`（默认）
- **专业版**：`http-pro.abuyun.com:9010`（特定账户）

### 💾 数据库配置

使用MongoDB作为主要数据存储，支持灵活的文档型数据存储。

#### 连接配置

在 `src/utils/MongoClient.py` 中配置数据库连接：

```python
# 默认连接配置
client = MongoClient('mongodb://localhost:27017/')
```

#### 数据库结构

- **企业新闻集合**：存储企业相关新闻数据
- **校友动态集合**：存储校友动态信息
- **标签集合**：存储AI生成的标签和分析结果
- **导入集合**：统一的数据导入集合

### 🌐 Chrome浏览器配置

系统使用Chrome浏览器的远程调试功能进行网页访问和代理管理。

#### 基本配置

- **调试端口**：9222
- **用户数据目录**：`C:\temp\chrome_debug_profile`
- **Chrome路径**：`C:\Program Files\Google\Chrome\Application\chrome.exe`

#### 启动参数

```bash
--remote-debugging-port=9222
--user-data-dir="C:\temp\chrome_debug_profile"
--disable-blink-features=AutomationControlled
--disable-web-security
```

#### 防检测配置

系统内置多种防检测机制：

- 禁用自动化控制特征
- 修改navigator.webdriver属性
- 使用真实的用户代理字符串
- 模拟人工操作行为

## 📊 数据流程

### 🎯 企业新闻采集流程

```mermaid
graph TD
    A[启动企业新闻采集器] --> B[从Laungee API获取企业列表]
    B --> C[初始化代理池和搜索器]
    C --> D[为每个企业构建搜索查询]
    D --> E[异步并发访问百度资讯]
    E --> F[解析搜索结果]
    F --> G[数据过滤和清洗]
    G --> H{是否需要内容提取?}
    H -->|是| I[提取网页完整内容]
    H -->|否| J[保存基础数据]
    I --> K[生成PDF文件]
    K --> L[Dify AI标签生成]
    L --> M[保存到Excel和MongoDB]
    J --> M
    M --> N[采集完成]
```

**详细步骤：**

1. **📋 获取企业列表**：从Laungee API获取待采集的企业基础信息
2. **🔍 构建搜索查询**：为每个企业生成搜索关键词和查询参数
3. **🚀 异步并发搜索**：使用asyncio和aiohttp并发访问百度资讯
4. **📄 解析搜索结果**：提取标题、摘要、链接、来源、发布时间等信息
5. **🧹 数据过滤清洗**：基于时间范围、内容相关性等条件过滤结果
6. **🌐 内容提取**（可选）：从新闻链接提取完整文章内容
7. **📄 PDF生成**（可选）：将网页内容转换为PDF文件并本地存储
8. **💾 数据存储**：同时保存到Excel文件和MongoDB数据库
9. **🤖 AI分析**（可选）：使用Dify AI工作流生成新闻标签和情感分析

### 🎓 校友动态采集流程

```mermaid
graph TD
    A[启动校友动态采集器] --> B[从API获取校友名单]
    B --> C[应用学校优先级逻辑]
    C --> D[初始化代理池和搜索器]
    D --> E[为每个校友构建搜索查询]
    E --> F[异步并发搜索动态]
    F --> G[解析搜索结果]
    G --> H[数据过滤和清洗]
    H --> I{是否需要内容提取?}
    I -->|是| J[提取网页完整内容]
    I -->|否| K[保存基础数据]
    J --> L[生成PDF文件]
    L --> M[保存到MongoDB和Excel]
    K --> M
    M --> N[采集完成]
```

**详细步骤：**

1. **🎓 获取校友名单**：从API获取指定学校的校友基础信息
2. **⚖️ 优先级处理**：应用复杂的学校优先级逻辑（已服务高校优先、复旦管院优先等）
3. **🔍 构建搜索查询**：结合校友姓名、单位等信息生成搜索关键词
4. **🚀 异步并发采集**：并发搜索每个校友的相关动态信息
5. **📄 结果解析**：提取动态标题、简介、链接、来源等信息
6. **🧹 数据清洗**：过滤无关内容，确保动态与校友的相关性
7. **🌐 内容提取**（可选）：从动态链接提取完整内容
8. **📄 PDF生成**（可选）：将内容转换为PDF文件
9. **💾 数据存储**：保存到MongoDB数据库和Excel文件

## ⚠️ 注意事项与最佳实践

### 🚦 使用限制

#### 并发控制

- **默认并发数**：5个并发请求，平衡效率与服务器压力
- **推荐设置**：
  - 测试环境：1-3个并发
  - 生产环境：5-10个并发
  - 高性能环境：10-20个并发（需要足够的代理IP）

#### 时间范围控制

- **企业新闻**：默认保留365天内的新闻
- **校友动态**：默认保留300天内的动态
- **建议设置**：根据实际需求调整，过长的时间范围会增加采集时间

#### 代理使用建议

- **强烈推荐**：使用代理池避免IP被封禁
- **代理类型**：优先使用动态代理，提供更好的匿名性
- **余额监控**：定期检查代理账户余额，确保服务不中断

### 🚀 性能优化

#### 异步处理优化

```python
# 推荐的并发配置
collector = EnterpriseNewsCollector(
    concurrent_limit=8,  # 适中的并发数
    use_proxy=True,      # 使用代理池
    keep_days=30         # 合理的时间范围
)
```

#### 网络优化

- **连接池**：使用aiohttp连接池，复用TCP连接
- **超时设置**：合理设置请求超时时间，避免长时间等待
- **重试机制**：智能重试失败的请求，提高成功率

#### 存储优化

- **批量写入**：使用批量操作减少数据库I/O
- **索引优化**：为MongoDB集合创建适当的索引
- **数据压缩**：PDF文件使用压缩存储，节省磁盘空间

### 🛡️ 错误处理与容错

#### 网络异常处理

- **自动重试**：网络超时和连接错误自动重试3次
- **指数退避**：重试间隔逐渐增加，避免频繁请求
- **代理切换**：代理失效时自动切换到备用代理

#### 解析异常处理

- **容错机制**：单个页面解析失败不影响整体采集
- **数据验证**：对提取的数据进行基本验证
- **降级处理**：主要提取器失败时使用备用提取方法

#### 日志与监控

- **详细日志**：记录采集过程中的关键信息和错误
- **进度显示**：实时显示采集进度和统计信息
- **异常报告**：自动记录和报告异常情况

### 💡 最佳实践

#### 采集策略

1. **分批采集**：大量数据分批处理，避免内存溢出
2. **增量更新**：定期采集新数据，避免重复采集
3. **数据去重**：基于URL和标题进行智能去重
4. **质量控制**：设置内容长度和相关性阈值

#### 资源管理

1. **内存管理**：及时释放不需要的数据对象
2. **文件管理**：定期清理临时文件和日志文件
3. **数据库维护**：定期优化数据库索引和清理过期数据

#### 安全考虑

1. **访问频率**：控制访问频率，避免被目标网站封禁
2. **用户代理**：使用真实的浏览器用户代理字符串
3. **会话管理**：合理管理浏览器会话，避免检测

## 🧪 测试与开发

### 调试与开发

#### 日志配置

系统提供详细的日志记录，便于开发和调试：

```python
# 日志文件位置
log/企业动态采集日志/     # 企业新闻采集日志
log/复旦大学动态采集日志/ # 校友动态采集日志
log/网页内容爬取日志/     # 网页内容提取日志
```

#### 开发环境配置

```bash
# 开发模式运行（详细日志）
python src/enterprise_news_collector.py --limit 5 --no-proxy

# 调试模式（单线程，详细输出）
python src/web_content_extractor.py --threads 1 --disable-dify
```

## 🚀 未来发展规划

### 🎯 功能扩展计划

#### 搜索引擎扩展

- [ ] **今日头条搜索**：集成头条搜索API，获取更多新闻源
- [ ] **搜狗搜索**：添加搜狗新闻搜索支持
- [ ] **360搜索**：集成360搜索引擎
- [ ] **谷歌新闻**：支持国际新闻搜索（需要特殊网络环境）

#### 内容提取增强

- [ ] **更多网站支持**：扩展对更多新闻网站的内容提取支持
- [ ] **视频内容提取**：支持从视频新闻中提取文字内容
- [ ] **图片OCR**：从新闻图片中提取文字信息
- [ ] **多语言支持**：支持英文等其他语言的新闻采集

#### AI功能升级

- [ ] **情感分析增强**：更精确的情感极性和强度分析
- [ ] **关键词提取**：自动提取新闻关键词和实体
- [ ] **摘要生成**：AI自动生成新闻摘要
- [ ] **相似度检测**：检测重复和相似新闻

#### 实时监控系统

- [ ] **实时采集**：支持实时监控和采集最新新闻
- [ ] **告警系统**：重要新闻自动告警通知
- [ ] **数据看板**：实时数据监控和可视化面板
- [ ] **API服务**：提供RESTful API接口供外部调用

## ❓ 常见问题与解决方案

### 🌐 Chrome浏览器相关问题

#### Q: Chrome浏览器远程调试启动失败怎么办？

**A: 多种解决方案**

```bash
# 方案1：手动启动Chrome远程调试模式
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug_profile"

# 方案2：使用相对路径
chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug_profile"

# 方案3：检查端口占用
netstat -ano | findstr :9222
```

**额外检查项：**

- 确保Chrome浏览器已正确安装
- 检查用户数据目录是否有写入权限
- 确认9222端口未被其他程序占用

#### Q: 浏览器被检测为自动化工具怎么办？

**A: 系统已内置多种防检测机制**

- 自动禁用webdriver特征
- 使用真实的用户代理字符串
- 模拟人工操作行为
- 如仍被检测，可尝试更换代理IP

### 🔗 代理相关问题

#### Q: 代理连接失败怎么处理？

**A: 分步排查解决**

1. **检查账户状态**

   ```bash
   # 登录阿布云控制台检查
   # - 账户余额是否充足
   # - 代理隧道是否过期
   # - 代理配置是否正确
   ```
2. **临时解决方案**

   ```bash
   # 跳过代理使用
   python src/enterprise_news_collector.py --no-proxy
   ```
3. **网络环境检查**

   - 检查防火墙设置
   - 确认网络连接稳定
   - 测试代理服务器连通性

#### Q: 代理自动续费失败怎么办？

**A: 检查以下项目**

- 账户余额是否充足（至少1元）
- 网络连接是否稳定
- 阿布云服务是否正常
- 手动登录阿布云控制台进行续费

### 💾 数据库相关问题

#### Q: MongoDB连接失败怎么办？

**A: 逐步检查解决**

1. **服务状态检查**

   ```bash
   # Windows检查MongoDB服务
   net start MongoDB

   # 检查服务是否运行
   tasklist | findstr mongod
   ```
2. **连接配置检查**

   - 检查 `src/utils/MongoClient.py`中的连接字符串
   - 确认数据库端口（默认27017）
   - 验证数据库访问权限
3. **网络连接测试**

   ```bash
   # 测试MongoDB连接
   mongo --host localhost --port 27017
   ```

#### Q: 数据保存失败怎么处理？

**A: 检查以下方面**

- MongoDB磁盘空间是否充足
- 数据格式是否符合要求
- 集合权限是否正确
- 查看详细错误日志

### 📊 采集效果相关问题

#### Q: 采集结果为空或很少怎么办？

**A: 多维度排查**

1. **搜索关键词优化**

   - 检查企业名称是否正确
   - 尝试使用企业简称或别名
   - 调整搜索关键词组合
2. **时间范围调整**

   ```bash
   # 扩大时间范围
   python src/enterprise_news_collector.py --keep-days 90
   ```
3. **网络状态检查**

   - 检查网络连接稳定性
   - 确认代理状态正常
   - 查看详细日志文件
4. **目标网站状态**

   - 确认百度资讯服务正常
   - 检查是否被反爬虫限制

#### Q: 如何提高采集效率？

**A: 多种优化策略**

1. **并发参数优化**

   ```bash
   # 适当增加并发数（根据网络环境调整）
   python src/enterprise_news_collector.py --concurrent-limit 10
   ```
2. **代理池优化**

   - 使用高质量代理服务
   - 确保代理IP池充足
   - 定期检查代理状态
3. **系统资源优化**

   - 确保充足的内存和CPU资源
   - 使用SSD硬盘提高I/O性能
   - 定期清理临时文件
4. **网络环境优化**

   - 使用稳定的网络连接
   - 避免网络高峰期采集
   - 考虑使用专线网络

### 📄 PDF功能相关问题

#### Q: PDF转换功能如何使用？

**A: 自动化PDF生成流程**

1. **自动转换**：系统会自动将网页内容转换为PDF格式
2. **存储位置**：PDF文件保存在 `output/pdf/`目录下
3. **文件命名**：使用URL的MD5哈希值作为文件名，确保唯一性
4. **数据关联**：在Excel和MongoDB中可查看对应的PDF文件名

#### Q: PDF生成失败怎么办？

**A: 检查以下项目**

- 确保Chrome浏览器正常运行
- 检查目标网页是否可正常访问
- 确认 `output/pdf/`目录有写入权限
- 查看详细错误日志

### 🔧 其他常见问题

#### Q: 程序运行缓慢怎么办？

**A: 性能优化建议**

- 减少并发数量，避免系统过载
- 使用更快的网络连接
- 增加系统内存
- 定期清理日志和临时文件

#### Q: 如何查看详细的运行日志？

**A: 日志文件位置**

```
log/企业动态采集日志/     # 企业新闻采集日志
log/复旦大学动态采集日志/ # 校友动态采集日志
log/网页内容爬取日志/     # 网页内容提取日志
```

---

**最后更新时间：2025-07-17**
