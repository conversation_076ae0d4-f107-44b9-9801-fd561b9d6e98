from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import os
import re
import sys
import hashlib
import base64
import traceback
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urlparse

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.browser_utils import LoginBrowser, IndependentBrowser
from daili import ProxyOperator

class WechatArticleExtractor:
    """
    微信公众号正文提取器，结构优化、内容清洗、返回结构化数据。
    支持单个和批量提取，适配Flask API调用。
    """
    def __init__(self, url=None, use_proxy=False, driver=None, thread_count=2):
        """
        初始化微信文章提取器

        Args:
            url (str): 单个URL，可选
            use_proxy (bool): 是否使用代理，默认False
            driver: 外部传入的浏览器驱动，可选
            thread_count (int): 线程数量，用于批量处理
        """
        self.url = url
        self.use_proxy = use_proxy
        self.thread_count = thread_count
        self.wechat_domains = ['mp.weixin.qq.com', 'weixin.qq.com']

        # 创建输出目录
        self.root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        self.output_dir = os.path.join(self.root_path, "output")
        self.pdf_output_dir = os.path.join(self.output_dir, "pdf")
        os.makedirs(self.pdf_output_dir, exist_ok=True)

        if driver:
            # 使用外部传入的驱动
            self.driver = driver
            self._self_driver = False
            self.drivers = [driver]
        else:
            # 初始化代理
            if use_proxy:
                try:
                    self.proxy_operator = ProxyOperator()
                    self.proxy = self.proxy_operator.proxy
                except Exception as e:
                    print(f"代理初始化失败: {e}")
                    self.proxy_operator = None
                    self.proxy = None
            else:
                self.proxy_operator = None
                self.proxy = None

            # 初始化浏览器驱动池
            self.drivers = []
            self._self_driver = True

            # 创建多个独立浏览器实例用于并发处理
            for i in range(self.thread_count):
                try:
                    # 使用独立浏览器创建实例
                    driver = IndependentBrowser.create_independent_browser(
                        headless=True,  # 微信提取使用无头模式
                        proxy_config=self.proxy if use_proxy else None
                    )

                    # 防止被检测
                    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                        "source": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
                    })

                    self.drivers.append(driver)
                    print(f"初始化微信提取器浏览器驱动 {i+1}/{self.thread_count}")

                except Exception as e:
                    print(f"初始化浏览器驱动 {i+1} 失败: {str(e)}")
                    continue

            if not self.drivers:
                raise Exception("所有浏览器驱动初始化失败")

            # 调整线程数量为实际成功的驱动数量
            if len(self.drivers) < self.thread_count:
                print(f"警告: 只成功初始化了 {len(self.drivers)} 个驱动，线程数量调整为 {len(self.drivers)}")
                self.thread_count = len(self.drivers)

            # 使用第一个驱动作为默认驱动
            self.driver = self.drivers[0] if self.drivers else None

    def fetch_html(self, url=None, driver=None, max_retries=3):
        """
        获取网页HTML内容

        Args:
            url (str): 要访问的URL，默认使用实例的url
            driver: 浏览器驱动，默认使用实例的driver
            max_retries (int): 最大重试次数

        Returns:
            tuple: (html_content, actual_url)
        """
        target_url = url or self.url
        target_driver = driver or self.driver

        if not target_url:
            raise ValueError("URL不能为空")
        if not target_driver:
            raise ValueError("浏览器驱动不可用")

        for attempt in range(max_retries):
            try:
                print(f"访问微信URL (第{attempt + 1}次): {target_url}")
                target_driver.get(target_url)
                time.sleep(3)  # 等待页面加载

                actual_url = target_driver.current_url
                html = target_driver.page_source

                # 检查是否成功加载微信页面
                if self.is_wechat_url(actual_url) and len(html) > 1000:
                    return html, actual_url
                else:
                    print(f"页面加载异常，准备重试...")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue

            except Exception as e:
                print(f"访问URL失败 (第{attempt + 1}次): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise

        raise Exception(f"访问URL失败，已重试{max_retries}次: {target_url}")

    def extract_content(self, url=None, driver=None, max_retries=3):
        """
        提取微信文章内容

        Args:
            url (str): 要提取的URL，默认使用实例的url
            driver: 浏览器驱动，默认使用实例的driver
            max_retries (int): 最大重试次数

        Returns:
            dict: 提取结果，包含url、title、content、html、actual_url等
        """
        target_url = url or self.url

        try:
            # 获取HTML内容
            html, actual_url = self.fetch_html(target_url, driver, max_retries)
            soup = BeautifulSoup(html, "html.parser")

            # 提取标题
            title_tag = soup.find("h1", id="activity-name")
            title = title_tag.get_text(strip=True) if title_tag else ""

            # 提取正文
            content_div = soup.find("div", id="js_content")
            paragraphs = []
            if content_div:
                for child in content_div.find_all(recursive=False):
                    text = child.get_text(separator=" ", strip=True)
                    if text:
                        paragraphs.append(self.clean_text(text))

            content = "\n".join(paragraphs)

            return {
                "success": True,
                "url": target_url,
                "actual_url": actual_url,
                "title": title,
                "content": content,
                "html": html,
                "extracted_at": datetime.now().isoformat(),
                "error": ""
            }

        except Exception as e:
            error_msg = f"提取微信内容失败: {str(e)}"
            print(error_msg)
            return {
                "success": False,
                "url": target_url,
                "actual_url": target_url,
                "title": "",
                "content": "",
                "html": "",
                "extracted_at": datetime.now().isoformat(),
                "error": error_msg
            }

    def clean_text(self, text):
        """
        内容清洗：去除多余空行、特殊符号、表情等

        Args:
            text (str): 原始文本

        Returns:
            str: 清洗后的文本
        """
        text = re.sub(r'\s+', ' ', text)  # 合并多余空白
        text = re.sub(r'[\u200b\u3000\xa0]', '', text)  # 去除特殊空白符
        text = re.sub(r'[\U0001F600-\U0001F64F]', '', text)  # 去除表情符号
        return text.strip()

    def is_wechat_url(self, url):
        """
        检查URL是否为微信链接

        Args:
            url (str): 要检查的URL

        Returns:
            bool: 是否为微信链接
        """
        try:
            parsed_url = urlparse(url)
            return any(domain in parsed_url.netloc for domain in self.wechat_domains)
        except Exception:
            return False

    def generate_pdf_and_save(self, original_url, actual_url, driver):
        """
        将微信文章转换为PDF并保存到本地文件

        Args:
            original_url (str): 原始URL（用于生成文件名）
            actual_url (str): 实际访问的URL
            driver: 浏览器驱动实例

        Returns:
            str: PDF文件名，如果生成失败返回空字符串
        """
        try:
            # 访问实际URL
            driver.get(actual_url)
            time.sleep(3)  # 等待页面完全加载

            # 使用Chrome的打印功能生成PDF
            print_options = {
                'landscape': False,
                'displayHeaderFooter': False,
                'printBackground': True,
                'preferCSSPageSize': True,
                'paperWidth': 8.27,  # A4纸宽度（英寸）
                'paperHeight': 11.69,  # A4纸高度（英寸）
                'marginTop': 0.4,
                'marginBottom': 0.4,
                'marginLeft': 0.4,
                'marginRight': 0.4
            }

            # 执行打印命令
            result = driver.execute_cdp_cmd('Page.printToPDF', print_options)
            base64_data = result['data']

            # 使用原始URL的MD5哈希值生成文件名
            url_hash = hashlib.md5(original_url.encode('utf-8')).hexdigest()
            pdf_filename = f"wx_{url_hash}.pdf"
            pdf_filepath = os.path.join(self.pdf_output_dir, pdf_filename)

            # 将base64数据解码并保存为PDF文件
            pdf_binary = base64.b64decode(base64_data)
            with open(pdf_filepath, 'wb') as f:
                f.write(pdf_binary)

            print(f"微信PDF保存成功: {pdf_filename}, 文件大小: {len(pdf_binary)} 字节")
            return pdf_filename

        except Exception as e:
            print(f"生成微信PDF时出错: 原始URL={original_url}, 实际URL={actual_url}, 错误: {str(e)}")
            traceback.print_exc()
            return ""

    def close(self):
        if self._self_driver:
            self.driver.quit()

    def extract_single_url(self, url, max_retries=3, generate_pdf=True):
        """
        提取单个微信URL的完整信息，包括内容和PDF
        这是一个独立的、可被API调用的方法

        Args:
            url (str): 微信文章URL
            max_retries (int): 最大重试次数
            generate_pdf (bool): 是否生成PDF文件

        Returns:
            dict: 完整的提取结果
        """
        if not self.is_wechat_url(url):
            return {
                "success": False,
                "url": url,
                "error": "不是有效的微信链接",
                "extracted_at": datetime.now().isoformat()
            }

        # 获取可用的驱动
        driver = self.driver
        if not driver:
            return {
                "success": False,
                "url": url,
                "error": "浏览器驱动不可用",
                "extracted_at": datetime.now().isoformat()
            }

        try:
            # 提取内容
            result = self.extract_content(url, driver, max_retries)

            # 如果提取成功且需要生成PDF
            if result["success"] and generate_pdf and result["content"]:
                pdf_filename = self.generate_pdf_and_save(url, result["actual_url"], driver)
                result["pdf_filename"] = pdf_filename
            else:
                result["pdf_filename"] = ""

            return result

        except Exception as e:
            return {
                "success": False,
                "url": url,
                "actual_url": url,
                "title": "",
                "content": "",
                "html": "",
                "pdf_filename": "",
                "extracted_at": datetime.now().isoformat(),
                "error": f"提取异常: {str(e)}"
            }

    def batch_extract(self, url_list, max_retries=3, concurrent_limit=None, generate_pdf=True):
        """
        批量提取多个微信公众号文章内容

        Args:
            url_list (list): 公众号文章url列表
            max_retries (int): 最大重试次数
            concurrent_limit (int): 并发限制，默认使用线程数
            generate_pdf (bool): 是否生成PDF文件

        Returns:
            list: 每个url提取结果的dict组成的列表
        """
        if not url_list:
            return []

        if concurrent_limit is None:
            concurrent_limit = min(self.thread_count, len(url_list))

        print(f"开始批量提取 {len(url_list)} 个微信URL，并发限制: {concurrent_limit}")

        results = []

        # 过滤有效的微信链接
        valid_urls = [url for url in url_list if self.is_wechat_url(url)]
        invalid_urls = [url for url in url_list if not self.is_wechat_url(url)]

        # 处理无效链接
        for invalid_url in invalid_urls:
            results.append({
                "success": False,
                "url": invalid_url,
                "error": "不是有效的微信链接",
                "extracted_at": datetime.now().isoformat()
            })

        if not valid_urls:
            return results

        # 使用线程池处理有效链接
        with ThreadPoolExecutor(max_workers=concurrent_limit) as executor:
            # 为每个URL分配驱动
            futures = []
            for i, url in enumerate(valid_urls):
                driver_index = i % len(self.drivers)
                driver = self.drivers[driver_index]
                future = executor.submit(self._extract_with_driver, url, driver, max_retries, generate_pdf)
                futures.append((future, url))

            # 收集结果
            for future, url in futures:
                try:
                    result = future.result()
                    results.append(result)
                    print(f"微信提取完成: {url}")
                except Exception as e:
                    error_result = {
                        "success": False,
                        "url": url,
                        "error": f"提取异常: {str(e)}",
                        "extracted_at": datetime.now().isoformat()
                    }
                    results.append(error_result)
                    print(f"微信提取失败: {url}, 错误: {str(e)}")

        successful_count = sum(1 for r in results if r.get('success', False))
        print(f"微信批量提取完成，成功: {successful_count}/{len(url_list)}")

        return results

    def _extract_with_driver(self, url, driver, max_retries, generate_pdf):
        """
        使用指定驱动提取单个URL的内容
        内部方法，用于线程池调用
        """
        try:
            result = self.extract_content(url, driver, max_retries)

            if result["success"] and generate_pdf and result["content"]:
                pdf_filename = self.generate_pdf_and_save(url, result["actual_url"], driver)
                result["pdf_filename"] = pdf_filename
            else:
                result["pdf_filename"] = ""

            return result

        except Exception as e:
            return {
                "success": False,
                "url": url,
                "error": f"提取异常: {str(e)}",
                "extracted_at": datetime.now().isoformat()
            }

# 示例用法
if __name__ == "__main__":
    url_list = [
        "https://mp.weixin.qq.com/s/v61LNATpM5PaZml1tK8z9A",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=vN1HVBGsDM-wc1krpSFiTMHf*t2gDfPHRvJ2n3hRNMs5XCfWxlg*cUz7jAkY*-pJ16wlyXyUOKMOduao9-9H5MNDMT4fpShEGG7hLYagY3JhASwLhVJAJWkbN1ia12S5&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=MT-ZG568FQ3YyYknKSVM2GFgdPtCcspsJsWDJkpKQLCc3vSuvn0jxLW14cZsbJuhKN65hxq3XYGZhjRO7UDydqaNZb1X*E2T9D21E0mcnRjckHMpxPghi-Yxr6qrY2Wg&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=0CMgkcRVNWWx4*6yls9EzOnWZAyQ7MrkXt7igCVfEti8SuGjxmILPyzvcQJhYptjqAQ2i5X5*hNiBswpQFISanJvULG18R-Pk1RXsxwsAu2iaSBXcJdJe5Q7wF30HGzX&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=Fea8Q0UPYH5JTFXln0meNp9S0mh1UpNQLj*5PYS8OKCcljZXUa2vq7Xo6skmfg1w0cq3cIAY9QjUT53TonJunRodeIjee6sQVWGhk8y7VnRKxldS4f7mHlkGgxYu1hnq&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=Pprf1EVF*FDPVZv*FjejsJpCBgGU2XV40IZ2tNKRLrxkzcz3zZeHN*NsWTPxkSU5V6AzVN9kv9Tkf*JUYiEc6SqHJnEwrJUvVP-hyC3UOYhhEZPuNmw5i5lmGHzB8Dip&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=ElC-BP9FN2Y*mHHj75qEry3D1vDUQMhLnDuHNnUUWz1Cu1pgKJmgEZxMyPi22gzsFnEQyLc51px7q8IdVfjoBNe5dahCdVjcARsEv6yh66SpHb39mg*zxQ0kr7DdBhn9&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=NPP4wQ6qkUgNnyduVjBmnsm4AFzJoJCi1mQa-ABBNEu6HS6AaktnvHwsUZVLdERMWiqUoS*ND-NArh0BWaV7p9aQpfxlwketW-6PLM41Isd9r*ulJHBoBY0zi2b5hiC2&new=1"
        # 可添加更多url
    ]
    extractor = WechatArticleExtractor()
    results = extractor.batch_extract(url_list)
    for result in results:
        print(f"标题: {result['title']}")
        print(result['content'])
        print("-"*60)
    extractor.close()
