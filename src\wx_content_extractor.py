from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import os
import re
import sys
import hashlib
import base64
import traceback
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urlparse

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.browser_utils import LoginBrowser, IndependentBrowser
from daili import ProxyOperator

class WechatArticleExtractor:
    """
    微信公众号正文提取器，结构优化、内容清洗、返回结构化数据。
    支持单个和批量提取，适配Flask API调用。
    """
    def __init__(self, url=None, use_proxy=False, driver=None, thread_count=2):
        """
        初始化微信文章提取器

        Args:
            url (str): 单个URL，可选
            use_proxy (bool): 是否使用代理，默认False
            driver: 外部传入的浏览器驱动，可选
            thread_count (int): 线程数量，用于批量处理
        """
        self.url = url
        self.use_proxy = use_proxy
        self.thread_count = thread_count
        self.wechat_domains = ['mp.weixin.qq.com', 'weixin.qq.com']

        # 创建输出目录
        self.root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        self.output_dir = os.path.join(self.root_path, "output")
        self.pdf_output_dir = os.path.join(self.output_dir, "pdf")
        os.makedirs(self.pdf_output_dir, exist_ok=True)

        if driver:
            # 使用外部传入的驱动
            self.driver = driver
            self._self_driver = False
            self.drivers = [driver]
        else:
            # 初始化代理
            if use_proxy:
                try:
                    self.proxy_operator = ProxyOperator()
                    self.proxy = self.proxy_operator.proxy
                except Exception as e:
                    print(f"代理初始化失败: {e}")
                    self.proxy_operator = None
                    self.proxy = None
            else:
                self.proxy_operator = None
                self.proxy = None

            # 初始化浏览器驱动池
            self.drivers = []
            self._self_driver = True

            # 创建多个独立浏览器实例用于并发处理
            for i in range(self.thread_count):
                try:
                    # 使用独立浏览器创建实例
                    driver = IndependentBrowser.create_independent_browser(
                        headless=True,  # 微信提取使用无头模式
                        proxy_config=self.proxy if use_proxy else None
                    )

                    # 防止被检测
                    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
                        "source": "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
                    })

                    self.drivers.append(driver)
                    print(f"初始化微信提取器浏览器驱动 {i+1}/{self.thread_count}")

                except Exception as e:
                    print(f"初始化浏览器驱动 {i+1} 失败: {str(e)}")
                    continue

            if not self.drivers:
                raise Exception("所有浏览器驱动初始化失败")

            # 调整线程数量为实际成功的驱动数量
            if len(self.drivers) < self.thread_count:
                print(f"警告: 只成功初始化了 {len(self.drivers)} 个驱动，线程数量调整为 {len(self.drivers)}")
                self.thread_count = len(self.drivers)

            # 使用第一个驱动作为默认驱动
            self.driver = self.drivers[0] if self.drivers else None

    def fetch_html(self):
        self.driver.get(self.url)
        time.sleep(2)  # 等待页面加载
        return self.driver.page_source

    def extract_content(self, url=None):
        url = url or self.url
        html = self.fetch_html()  # fetch_html 里已用 self.url
        soup = BeautifulSoup(html, "html.parser")
        # 提取标题
        title_tag = soup.find("h1", id="activity-name")
        title = title_tag.get_text(strip=True) if title_tag else ""
        # 提取正文
        content_div = soup.find("div", id="js_content")
        paragraphs = []
        if content_div:
            for child in content_div.find_all(recursive=False):
                text = child.get_text(separator=" ", strip=True)
                if text:
                    paragraphs.append(self.clean_text(text))
        content = "\n".join(paragraphs)
        return {"url": url, "title": title, "content": content, "html": html}

    def clean_text(self, text):
        # 内容清洗：去除多余空行、特殊符号、表情等
        text = re.sub(r'\s+', ' ', text)  # 合并多余空白
        text = re.sub(r'[\u200b\u3000\xa0]', '', text)  # 去除特殊空白符
        text = re.sub(r'[\U0001F600-\U0001F64F]', '', text)  # 去除表情符号
        return text.strip()

    def close(self):
        if self._self_driver:
            self.driver.quit()

    def batch_extract(self, url_list):
        """
        批量提取多个微信公众号文章内容。
        Args:
            url_list (list): 公众号文章url列表
        Returns:
            list: 每个url提取结果的dict组成的列表
        """
        results = []
        for url in url_list:
            try:
                self.url = url
                result = self.extract_content()
                results.append(result)
            except Exception as e:
                print(f"提取失败: {url}, 错误: {e}")
        return results

# 示例用法
if __name__ == "__main__":
    url_list = [
        "https://mp.weixin.qq.com/s/v61LNATpM5PaZml1tK8z9A",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=vN1HVBGsDM-wc1krpSFiTMHf*t2gDfPHRvJ2n3hRNMs5XCfWxlg*cUz7jAkY*-pJ16wlyXyUOKMOduao9-9H5MNDMT4fpShEGG7hLYagY3JhASwLhVJAJWkbN1ia12S5&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=MT-ZG568FQ3YyYknKSVM2GFgdPtCcspsJsWDJkpKQLCc3vSuvn0jxLW14cZsbJuhKN65hxq3XYGZhjRO7UDydqaNZb1X*E2T9D21E0mcnRjckHMpxPghi-Yxr6qrY2Wg&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=0CMgkcRVNWWx4*6yls9EzOnWZAyQ7MrkXt7igCVfEti8SuGjxmILPyzvcQJhYptjqAQ2i5X5*hNiBswpQFISanJvULG18R-Pk1RXsxwsAu2iaSBXcJdJe5Q7wF30HGzX&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=Fea8Q0UPYH5JTFXln0meNp9S0mh1UpNQLj*5PYS8OKCcljZXUa2vq7Xo6skmfg1w0cq3cIAY9QjUT53TonJunRodeIjee6sQVWGhk8y7VnRKxldS4f7mHlkGgxYu1hnq&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=Pprf1EVF*FDPVZv*FjejsJpCBgGU2XV40IZ2tNKRLrxkzcz3zZeHN*NsWTPxkSU5V6AzVN9kv9Tkf*JUYiEc6SqHJnEwrJUvVP-hyC3UOYhhEZPuNmw5i5lmGHzB8Dip&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=ElC-BP9FN2Y*mHHj75qEry3D1vDUQMhLnDuHNnUUWz1Cu1pgKJmgEZxMyPi22gzsFnEQyLc51px7q8IdVfjoBNe5dahCdVjcARsEv6yh66SpHb39mg*zxQ0kr7DdBhn9&new=1",
        "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=NPP4wQ6qkUgNnyduVjBmnsm4AFzJoJCi1mQa-ABBNEu6HS6AaktnvHwsUZVLdERMWiqUoS*ND-NArh0BWaV7p9aQpfxlwketW-6PLM41Isd9r*ulJHBoBY0zi2b5hiC2&new=1"
        # 可添加更多url
    ]
    extractor = WechatArticleExtractor()
    results = extractor.batch_extract(url_list)
    for result in results:
        print(f"标题: {result['title']}")
        print(result['content'])
        print("-"*60)
    extractor.close()
