import time
import urllib.parse as urlparse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import requests
import json
from datetime import datetime
import os

class WechatArticleCrawler:
    """微信公众号文章爬虫"""
    
    def __init__(self):
        self.token = None
        self.cookies = None
        self.driver = None
        self.setup_driver()
    
    def setup_driver(self):
        """初始化Chrome浏览器"""
        options = Options()
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 使用标准selenium
        self.driver = webdriver.Chrome(options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    def get_cookies_and_token(self):
        """获取cookies和token"""
        print("开始登录微信公众平台...")
        self.driver.get("https://mp.weixin.qq.com/")
        
        try:
            # 等待二维码出现
            WebDriverWait(self.driver, 10).until(
                EC.visibility_of_element_located((By.CLASS_NAME, "login__type__container__scan__qrcode"))
            )
            print("请扫描二维码登录...")
            
            # 等待登录成功
            while True:
                current_url = self.driver.current_url
                if "token" in current_url:
                    print("登录成功，检测到token 准备进行跳转")
                    parsed_url = urlparse.urlparse(current_url)
                    self.token = urlparse.parse_qs(parsed_url.query).get("token", [""])[0]
                    print(f"获取到token: {self.token}")
                    
                    # 跳转到编辑页面
                    timestamp = int(time.time() * 1000)
                    new_url = (
                        f"https://mp.weixin.qq.com/cgi-bin/appmsg?"
                        f"t=media/appmsg_edit_v2&action=edit&isNew=1&type=77&createType=0"
                        f"&token={self.token}&lang=zh_CN&timestamp={timestamp}"
                    )
                    
                    self.driver.get(new_url)
                    print(f"跳转到新页面: {new_url}")
                    
                    # 点击超链接按钮
                    WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.ID, "js_editor_insertlink"))
                    ).click()
                    print("已自动点击超链接")
                    
                    # 点击选择其他公众号
                    WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, ".weui-desktop-btn.weui-desktop-btn_default"))
                    ).click()
                    print("已自动点击选择其他公众号")
                    
                    # 输入搜索关键词
                    input_box = WebDriverWait(self.driver, 50).until(
                        EC.visibility_of_element_located(
                            (By.XPATH, "//input[@class='weui-desktop-form__input' and @placeholder='输入文章来源的账号名称或微信号，回车进行搜索']")
                        )
                    )
                    print("已自动键入北京")
                    input_box.send_keys("北京")
                    
                    # 点击搜索按钮
                    search_button = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".weui-desktop-icon.weui-desktop-icon__search"))
                    )
                    self.driver.execute_script("arguments[0].click();", search_button)
                    print("已自动点击搜索")
                    
                    # 等待搜索完成
                    time.sleep(3)
                    
                    # 获取cookies
                    selenium_cookies = self.driver.get_cookies()
                    self.cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
                    print("成功获取cookies")
                    
                    print("自动化爬虫完毕")
                    return True
                    
                time.sleep(1)
                
        except Exception as e:
            print(f"获取cookies和token失败: {e}")
            return False
    
    def search_biz(self, query, count=10):
        """搜索公众号"""
        if not self.token or not self.cookies:
            print("请先获取token和cookies")
            return []
        
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
        
        params = {
            'action': 'search_biz',
            'token': self.token,
            'lang': 'zh_CN',
            'f': 'json',
            'ajax': '1',
            'query': query,
            'count': count
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://mp.weixin.qq.com/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        try:
            response = requests.get(search_url, params=params, headers=headers, cookies=self.cookies)
            if response.status_code == 200:
                data = response.json()
                if data.get('base_resp', {}).get('ret') == 0:
                    return data.get('list', [])
            
            print(f"搜索失败: {response.status_code}, {response.text}")
            return []
            
        except Exception as e:
            print(f"搜索异常: {e}")
            return []
    
    def get_articles_by_fakeid(self, fakeid, count=10):
        """根据公众号fakeid获取文章列表"""
        if not self.token or not self.cookies:
            print("请先获取token和cookies")
            return []
        
        url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
        
        params = {
            'action': 'list_ex',
            'begin': '0',
            'count': str(count),
            'fakeid': fakeid,
            'type': '9',
            'token': self.token,
            'lang': 'zh_CN',
            'f': 'json',
            'ajax': '1'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://mp.weixin.qq.com/',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, cookies=self.cookies)
            if response.status_code == 200:
                data = response.json()
                if data.get('base_resp', {}).get('ret') == 0:
                    return data.get('app_msg_list', [])
            
            print(f"获取文章失败: {response.status_code}, {response.text}")
            return []
            
        except Exception as e:
            print(f"获取文章异常: {e}")
            return []
    
    def save_articles_to_file(self, articles, filename=None):
        """保存文章到文件"""
        if not filename:
            filename = f"wechat_articles_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)
        
        print(f"文章已保存到: {filepath}")
    
    def run_crawler(self, search_query="北京", article_count=5):
        """运行爬虫主流程"""
        print("开始运行微信公众号文章爬虫...")
        
        # 1. 获取cookies和token
        if not self.get_cookies_and_token():
            print("获取认证信息失败")
            return
        
        # 2. 搜索公众号
        print(f"搜索公众号: {search_query}")
        biz_list = self.search_biz(search_query)
        
        if not biz_list:
            print("未找到相关公众号")
            return
        
        print(f"找到 {len(biz_list)} 个公众号")
        
        all_articles = []
        
        # 3. 获取每个公众号的文章
        for i, biz in enumerate(biz_list[:3]):  # 只处理前3个公众号
            nickname = biz.get('nickname', '未知')
            fakeid = biz.get('fakeid', '')
            
            print(f"正在获取公众号 '{nickname}' 的文章...")
            
            articles = self.get_articles_by_fakeid(fakeid, article_count)
            
            for article in articles:
                article['biz_nickname'] = nickname
                article['biz_fakeid'] = fakeid
                all_articles.append(article)
            
            print(f"获取到 {len(articles)} 篇文章")
            
            if i < len(biz_list[:3]) - 1:  # 不是最后一个
                time.sleep(2)  # 避免请求过快
        
        # 4. 保存结果
        if all_articles:
            self.save_articles_to_file(all_articles)
            print(f"总共获取到 {len(all_articles)} 篇文章")
        else:
            print("未获取到任何文章")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    crawler = WechatArticleCrawler()
    
    try:
        # 运行爬虫，搜索关键词可以修改
        crawler.run_crawler(search_query="北京", article_count=5)
        
    except KeyboardInterrupt:
        print("用户中断程序")
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        crawler.close()

if __name__ == "__main__":
    main()

