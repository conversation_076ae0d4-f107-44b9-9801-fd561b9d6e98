from flask import Flask
from config import Config
import sys
import os
def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

    # 注册主路由蓝图
    try:
        from app.routes.routes import bp as main_bp
        app.register_blueprint(main_bp)
        print("✓ 主路由蓝图注册成功")
    except ImportError as e:
        print(f"✗ 主路由蓝图导入失败: {e}")

    # 注册企业提取器蓝图
    try:
        from app.routes.enterprise_extractor import enterprise_extractor_bp
        app.register_blueprint(enterprise_extractor_bp)
        print("✓ 企业提取器蓝图注册成功")
    except ImportError as e:
        print(f"✗ 企业提取器蓝图导入失败: {e}")
        sys.exit(1)

    # 注册微信提取器蓝图
    try:
        from app.routes.wechat_extractor import wechat_extractor_bp
        app.register_blueprint(wechat_extractor_bp)
        print("✓ 微信提取器蓝图注册成功")
    except ImportError as e:
        print(f"✗ 微信提取器蓝图导入失败: {e}")
        sys.exit(1)

    return app

if __name__ == '__main__':
    
    print("启动Flask应用...")
    try:
        app = create_app()
        print("✓ Flask应用创建成功")
        print("启动服务器...")
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        sys.exit(1)
