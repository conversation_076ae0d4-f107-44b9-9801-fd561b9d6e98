# -*- coding: utf-8 -*-
"""
数据处理工具模块

本模块提供数据处理相关的功能，包括：
- 数据清洗和验证
- 数据过滤
- 通用数据处理函数
"""

from datetime import datetime, timedelta


class DataUtils:
    """数据处理工具类，提供各种数据处理功能"""

    @staticmethod
    def has_name_in_content(name, title, text):
        """
        检查标题和摘要是否包含企业名称

        Args:
            name (str): 企业名称
            title (str): 新闻标题
            text (str): 新闻摘要

        Returns:
            bool: 如果标题或摘要包含企业名称则返回True，否则返回False
        """
        return name in title + text

    @staticmethod
    def within_days(date, keep_days):
        """
        检查日期是否在指定天数内

        Args:
            date (str): 格式为'%Y-%m-%d'的日期字符串
            keep_days (int): 保留的天数

        Returns:
            bool: 如果日期在当前日期的keep_days天内则返回True，否则返回False
        """
        if not date:
            return False

        try:
            date = datetime.strptime(date, '%Y-%m-%d')
            return datetime.now() - date <= timedelta(days=keep_days)
        except ValueError:
            return False

    @staticmethod
    def clean_result(name, title, summary, date):
        """
        清洗和处理采集结果数据的函数

        Args:
            name (str): 企业名称
            title (str): 新闻标题
            summary (str): 新闻摘要
            date (str): 发布日期

        Returns:
            tuple: 处理后的(标题, 摘要, 日期)元组
        """
        if date:
            return title, summary, date
        elif name == summary:
            return '', '', title
        else:
            return title, summary, date

    @staticmethod
    def clean_text(text):
        """
        清理文本中的特殊字符

        Args:
            text (str): 需要清理的文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return ''

        # 替换常见的标点符号
        text = text.replace(',', '，').replace('.', '。')

        # 去除首尾空白字符
        text = text.strip()

        return text

    @staticmethod
    def validate_url(url):
        """
        验证URL是否有效

        Args:
            url (str): 需要验证的URL

        Returns:
            bool: URL有效返回True，否则返回False
        """
        if not url:
            return False

        # 基本的URL格式检查
        if url.startswith('http://') or url.startswith('https://'):
            return True

        return False

    @staticmethod
    def process_baijia_url(url):
        """
        处理百家号链接的HTTPS转换

        Args:
            url (str): 原始URL

        Returns:
            str: 处理后的URL
        """
        if 'baijiahao.baidu.com' in url and 'https' not in url:
            url = url.replace('http', 'https', 1)

        return url
