from urllib import parse
from pymongo import MongoClient as PyMongoClient

class monclient(PyMongoClient):
    def __init__(self, *, user="usr_spider", password="laungee123456", db="spider", collection="test"):
        # 处理用户名和密码
        self.user = parse.quote_plus(user)
        self.passwd = parse.quote_plus(password)
        
        # 调用父类构造函数
        connection_string = f'mongodb://{self.user}:{self.passwd}@192.168.1.230:27017/spider'
        super().__init__(connection_string)
        
        # 设置数据库和集合
        self.db = self[db]
        if collection in self.get_all_col():
            self.collection = self.db[collection]
        else:
            print("集合不存在")
            self.collection = self.db["test"]
    
    # 保留特有的方法
    def change_col(self, col_name):
        self.collection = self.db[col_name]

    def change_db(self, db_name, col_name):
        self.db = self[db_name]
        self.collection = self.db[col_name]

    def get_all_col(self):
        return self.db.list_collection_names()

    def create_col(self, col_name):
        self.db.create_collection(col_name)
        print(f"新集合{col_name}已创建！！！")
        
    # 为了保持接口一致，添加代理方法
    def insert_one(self, insert_dict):
        return self.collection.insert_one(insert_dict)
        
    def insert_many(self, insert_dict_list):
        return self.collection.insert_many(insert_dict_list)

    def close(self):
        """关闭MongoDB连接"""
        super().close()
