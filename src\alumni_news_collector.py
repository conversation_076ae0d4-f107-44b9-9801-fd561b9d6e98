# -*- coding: utf-8 -*-
"""
复旦大学校友动态采集器

本模块专门用于采集复旦大学校友的动态信息，通过百度资讯进行搜索和采集。
主要功能：
- 从API获取复旦大学校友名单
- 异步采集校友相关的新闻动态
- 数据过滤和清洗
- 保存到MongoDB数据库

历史更新记录：
3.22: 单独采集复旦大学的动态，周一凌晨一点运行，单独采集获取名单时使用的是get_df_data
"""
import traceback
from datetime import datetime
import pandas as pd
import re
import sys
import time
import asyncio
import aiohttp
import os

# 导入重构后的工具模块
from utils.api_utils import LaungeeAPIUtils
from utils.search_utils import BaiduNewsSearcher
from utils.time_utils import TimeUtils
from utils.data_utils import DataUtils
from utils.MongoClient import monclient
from daili import ProxyOperator

# 将项目根目录添加到路径
root_path = os.path.abspath(os.path.join(os.path.abspath(__file__), '..', '..'))
sys.path.append(root_path)


class AlumniNewsCollector:
    """复旦大学校友动态采集器类"""
    
    def __init__(self, target_school='复旦大学', use_proxy=True, keep_days=14,
                 keep_notes=True, addition_times=1, concurrent_limit=5, limit=None):
        """
        初始化校友动态采集器

        Args:
            target_school (str): 目标采集学校，默认为复旦大学
            use_proxy (bool): 是否使用代理池，默认为True
            keep_days (int): 保留天数，默认为14天
            keep_notes (bool): 是否保留备注，默认为True
            addition_times (int): 补采次数，默认为1次
            concurrent_limit (int): 并发限制，默认为5
            limit (int, optional): 限制采集的校友人数，用于测试，默认为None（不限制）
        """
        # 基本配置
        self.target_school = target_school
        self.use_proxy = use_proxy
        self.keep_days = keep_days
        self.keep_notes = keep_notes
        self.addition_times = addition_times
        self.concurrent_limit = concurrent_limit
        self.limit = limit
        
        # 时间相关
        self.start_time = time.perf_counter()
        self.collection_time = datetime.now().strftime('%Y-%m-%d %H-%M-%S')
        
        # 初始化工具类实例
        self.time_utils = TimeUtils()  # 需要实例化，因为有预构建的匹配模式

        # 动态姓名一定范围内含有这些关键词，则丢弃这条动态
        self.filter_keywords = [
            '记者', '报导', '实习生', '通讯员', '监制', '策划', '编辑', '音频制作', '视频导演', '视频拍摄',
            '后期制作', '出品人', '监制', '总策划', '文案', '配音', '书法', '动画', '航拍', '绘画', '演员', '责编',
            '统筹'
        ]
        
        # 日志配置
        self.log_filename = f'{self.target_school}校友动态采集日志_{self.collection_time}.txt'
        self.log_dir = os.path.join(root_path, 'log', f'{self.target_school}动态采集日志')
        os.makedirs(self.log_dir, exist_ok=True)
        self.log_file_path = os.path.join(self.log_dir, self.log_filename)
        
        # 输出配置
        self.output_filename = f'{self.target_school}校友动态采集结果_{self.collection_time}.xlsx'
        self.output_dir = os.path.join(root_path, 'output')
        os.makedirs(self.output_dir, exist_ok=True)
        self.output_file = os.path.join(self.output_dir, self.output_filename)
        
        # 初始化百度搜索器
        self.baidu_searcher = BaiduNewsSearcher(use_proxy=self.use_proxy)
        
        # 已服务高校名单
        self.served_schools = [
            '复旦大学', '中国科学技术大学', '上海交通大学', '华东师范大学', '同济大学', '四川大学', '上海大学',
            '中国药科大学', '复旦大学管理学院', '山东大学', '东南大学',
            '华东理工大学', '华北电力大学', '上海中医药大学', '广东财经大学', '上海海事大学', '中国海洋大学',
            '首都经济贸易大学', '西南大学', '北京工业大学', '中国科学院大学',
            '南京信息工程大学', '广东石油化工大学', '上海立信会计金融学院', '南昌大学'
        ]
        
        # 全局计数器
        self.total_count = 0
        self.current_count = 0
        
    
    def _filter_same(self, save_data, keep_days):
        """
        过滤重复和无效数据

        Args:
            save_data (list): 待过滤的数据列表，每个元素为包含[id, name, source, title, summary, link, source_name, date]的列表
            keep_days (int): 保留天数

        Returns:
            list: 过滤后的数据列表
        """
        length = 15  # 重复检测的字符串长度
        drop_list = []

        # 去除标题和简介中不含有姓名的数据
        error_collect = []
        for i in save_data:
            if not DataUtils.has_name_in_content(i[1], i[3], i[4]):  # i[1]=name, i[3]=title, i[4]=summary
                error_collect.append(i)
        for i in error_collect:
            save_data.remove(i)

        # 去除keep_days时间以外的数据
        error_time = []
        for i in save_data:
            if not self.time_utils.within_days(i[7], keep_days):  # i[7]=date
                error_time.append(i)
        for i in error_time:
            save_data.remove(i)

        # 去除重复数据，按照标题或摘要中含有length个长度的重复
        for i in range(len(save_data)):
            for j in range(i + 1, len(save_data)):
                # 完全相同的标题或摘要
                if (save_data[i][3] == save_data[j][3]) or (save_data[i][4] == save_data[j][4]):
                    drop_list.append(save_data[i])
                    break

                # 标题部分重复检测
                if len(save_data[i][3]) > length:
                    title_same = False
                    for start in range(len(save_data[i][3]) - length + 1):
                        substring = save_data[i][3][start:start + length]
                        if substring in save_data[j][3]:
                            drop_list.append(save_data[i])
                            title_same = True
                            break
                    if title_same:
                        break

                # 摘要部分重复检测
                if len(save_data[i][4]) > length:
                    summary_same = False
                    for start in range(len(save_data[i][4]) - length + 1):
                        substring = save_data[i][4][start:start + length]
                        if substring in save_data[j][4]:
                            drop_list.append(save_data[i])
                            summary_same = True
                            break
                    if summary_same:
                        break

        # 移除重复数据
        for i in drop_list:
            if i in save_data:
                save_data.remove(i)

        # 去除含有关键词的数据，标题中为姓名前后5个字的范围，摘要中姓名前10后5个字的范围
        has_keyword = []
        for i in save_data:
            name = i[1]
            title = i[3]
            summary = i[4]

            # 在标题中查找姓名位置
            title_positions = [(match.start(), match.end()) for match in re.finditer(name, title)]
            # 在摘要中查找姓名位置
            summary_positions = [(match.start(), match.end()) for match in re.finditer(name, summary)]

            for keyword in self.filter_keywords:
                # 检查标题中姓名前后5个字的范围
                title_has_keyword = False
                for position in title_positions:
                    start_pos = max(position[0] - 5, 0)
                    end_pos = position[1] + 5
                    if keyword in title[start_pos:end_pos]:
                        has_keyword.append(i)
                        title_has_keyword = True
                        break
                if title_has_keyword:
                    break

                # 检查摘要中姓名前10后5个字的范围
                summary_has_keyword = False
                for position in summary_positions:
                    start_pos = max(position[0] - 10, 0)
                    end_pos = position[1] + 5
                    if keyword in summary[start_pos:end_pos]:
                        has_keyword.append(i)
                        summary_has_keyword = True
                        break
                if summary_has_keyword:
                    break

        # 移除含有关键词的数据
        for i in has_keyword:
            if i in save_data:
                save_data.remove(i)

        return save_data
    
    def save_data(self, school_name, data_param, log_file):
        """
        保存采集到的数据到MongoDB数据库
        
        Args:
            school_name (str): 学校名称
            data_param (DataFrame): 要保存的数据
            log_file (file): 日志文件对象
        """
        print('正在将{}的数据写入数据库...'.format(school_name))
        print('正在将{}的数据写入数据库...'.format(school_name), file=log_file)
        
        # 按姓名和单位排序
        data_param = data_param.sort_values(by=['姓名', '单位']).reset_index(drop=True)

        # 连接MongoDB数据库
        client = monclient()
        collection = client.crawlab.DB_DATA_NEWS_alumniNews
        import_collection = client.crawlab.DB_IMPORT_COLLECT_TEMPNEWS
        
        # 逐行保存数据
        for i in data_param.index:
            # 校友新闻数据字典
            alumni_news_dict = {
                '采集来源': data_param.loc[i, '采集来源'],
                'ID': data_param.loc[i, 'ID'],
                '学校': data_param.loc[i, '学校'],
                '姓名': data_param.loc[i, '姓名'],
                '单位': data_param.loc[i, '单位'],
                '标题': data_param.loc[i, '标题'],
                '摘要': data_param.loc[i, '摘要'],
                '标题链接': data_param.loc[i, '标题链接'],
                '来源': data_param.loc[i, '来源'],
                '发布时间': data_param.loc[i, '发布时间'],
                '教育经历': data_param.loc[i, '教育经历'],
                "采集时间": self.collection_time
            }
            
            # 导入集合数据字典
            import_dict = {
                "采集名单中校友ID": data_param.loc[i, 'ID'],
                "来源名称": data_param.loc[i, '来源'],
                "来源链接": data_param.loc[i, '标题链接'],
                "来源发布时间": data_param.loc[i, '发布时间'],
                "采集时间": self.collection_time,
                "动态标题": data_param.loc[i, '标题'],
                "动态简介": data_param.loc[i, '摘要'],
                "关联主体名称": data_param.loc[i, '姓名'],
                "关联主体描述": data_param.loc[i, '教育经历'],
                "关联学校": data_param.loc[i, '学校'],
                "关联主体单位": data_param.loc[i, '单位'],
            }
            
            # 保存到MongoDB数据库
            collection.insert_one(alumni_news_dict)
            import_collection.insert_one(import_dict)
        
        client.close()

    async def crawl_single_alumni_news(self, result_data, keyword_info, proxy_operator, semaphore, idx):
        """
        异步采集单个校友的动态信息

        Args:
            result_data (list): 存储采集结果的列表
            keyword_info (dict): 校友信息字典，包含姓名、单位等
            proxy_operator (ProxyOperator): 代理操作对象
            semaphore (asyncio.Semaphore): 并发控制信号量
            idx (int): 当前采集索引
        """
        collected_data = []  # 保存采集到的结果


        if self.use_proxy and proxy_operator:
            proxy_operator.check_proxy_pool()  # 检查代理，如果代理时间过了就充钱延长代理时间

        # 构建搜索关键词
        search_query = keyword_info['姓名'] + ' ' + keyword_info['单位']

        async with semaphore:
            # 执行补充采集
            for _ in range(self.addition_times + 1):
                try:
                    await asyncio.sleep(1)  # 限制请求频率，每个任务至少占用1秒

                    # 使用新的百度搜索器进行异步搜索
                    search_results = await self.baidu_searcher.async_search(
                        search_query,
                        max_retries=1,
                        proxy_operator=proxy_operator if self.use_proxy else None
                    )

                    if search_results:
                        # 将搜索结果转换为原有格式
                        for result in search_results:
                            # 提取并清理标题
                            title = DataUtils.clean_text(result['title'])
                            # 提取并清理摘要
                            summary = DataUtils.clean_text(result['summary'])
                            href = result['url']
                            source = result['source']
                            date_time = result['publish_time']

                            # 处理百家号链接的HTTPS转换
                            if date_time == '':
                                href = DataUtils.process_baijia_url(href)

                            collected_data.append([
                                '百度资讯', keyword_info['姓名'], keyword_info['单位'], title, summary,
                                href, source, date_time, keyword_info["教育经历"]
                            ])
                        break

                except asyncio.TimeoutError:
                    pass
                except aiohttp.ClientHttpProxyError:
                    if self.use_proxy and proxy_operator:
                        proxy_operator.check_proxy_pool()  # 检查代理池是否过期
                except Exception:
                    pass


            # 对采集到的所有动态进行过滤，留下符合要求的动态
            collected_data = self._filter_same(collected_data, self.keep_days)
            self.current_count += len(collected_data)

            # 将采集到的列表类型的结果转化为DataFrame类型
            save_df = pd.DataFrame(data=collected_data,
                                   columns=['采集来源', '姓名', '单位', '标题', '摘要', '标题链接', '来源', '发布时间', "教育经历"])

            # 将keyword_info的其他列名加入到结果中
            save_df = pd.concat([
                save_df,
                pd.DataFrame([keyword_info.drop(['姓名', '单位', "教育经历"])] * len(save_df)).reset_index(drop=True)
            ], axis=1)

            # 将这个人采集到的结果加入到该学校的总数据中
            for _, row in save_df.iterrows():
                result_data.append(row.tolist())

            used_time = int(time.perf_counter() - self.start_time)
            print('采集进度:{}/{}，用时{}分{}秒，采集数量：{} ,总数量：{}   '.format(
                idx + 1, self.total_count, int(used_time // 60), int(used_time % 60), len(save_df), self.current_count))

    async def crawl_alumni_news(self, alumni_df):
        """
        异步采集校友新闻

        Args:
            alumni_df (DataFrame): 校友名单数据

        Returns:
            DataFrame: 采集到的新闻数据
        """
        df_result = {}
        semaphore = asyncio.Semaphore(self.concurrent_limit)  # 限制并发数

        # 根据配置决定是否初始化代理
        proxy_operator = None
        if self.use_proxy:
            proxy_operator = ProxyOperator('HT99W04OL07C735D', 'A767B2BC2CB312EE')
            print(f"已初始化阿布云代理池")
        else:
            print(f"未使用代理池，将直接连接")

        # 打开日志文件
        with open(self.log_file_path, 'a+', encoding='utf-8') as log_file:
            print(f'即将采集：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}', file=log_file)
            print(f'采集完毕后补采{self.addition_times}次，共采集{self.addition_times + 1}次。'.replace('补采0次', '不进行补采'))

            # 处理每个学校的校友
            school_name = self.target_school
            time_start = time.perf_counter()

            try:
                time.sleep(1)
                print('\n', '正在采集 {} {}:                            '.format(school_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
                print('\n', '正在采集 {} 【{}】:                            '.format(school_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                      file=log_file)  # 写入日志文件

                # 清理姓名中的控制字符
                alumni_df['姓名'] = alumni_df.apply(lambda x: self.time_utils.control_char_re.sub('', x['姓名']), axis=1)

                result_data = []  # 记录该学校采集的结果
                self.current_count = 0  # 重置当前计数
                self.total_count = len(alumni_df)  # 记录该学校需要采集的名单数量
                tasks = []

                for idx, keyword in alumni_df.iterrows():
                    # 将每个名单采集的代码作为一个任务，并发采集
                    tasks.append(asyncio.create_task(
                        self.crawl_single_alumni_news(result_data, keyword, proxy_operator, semaphore, idx)))

                await asyncio.wait(tasks)

                if len(result_data) == 0:
                    print('{}没有采集到信息, 总用时{}分'.format(school_name, int((time.perf_counter() - self.start_time) // 60)))
                    print('{}没有采集到信息, 总用时{}分'.format(school_name, int((time.perf_counter() - self.start_time) // 60)),
                          file=log_file)  # 写入日志文件
                    return pd.DataFrame()

                # 将列表类型的结果转化为DataFrame类型
                df_result[school_name] = pd.DataFrame(data=result_data,
                                              columns=['采集来源', '姓名', '单位', '标题', '摘要', '标题链接', '来源',
                                                       '发布时间', '教育经历'] + alumni_df.columns.drop(
                                                  ['姓名', '单位', '教育经历']).tolist())

            except Exception as e:
                print('{}采集失败，失败信息：'.format(school_name), traceback.format_exc())
                print('{}采集失败，失败信息：{}'.format(school_name, e), traceback.format_exc(), file=log_file)  # 写入日志文件
                return pd.DataFrame()
            else:
                used_time = int(time.perf_counter() - time_start)
                try:
                    # 保存结果到MongoDB
                    # self.save_data(school_name, df_result[school_name], log_file)
                    pass
                except Exception as e:
                    print('{}保存失败，失败信息：'.format(school_name), traceback.format_exc())
                    print('{}保存失败，失败信息：{}'.format(school_name, e), traceback.format_exc(), file=log_file)  # 写入日志文件

                print("采集完毕，用时{}分{}秒。总用时{}分                            ".format(int(used_time // 60),
                                                                                            int(used_time % 60), int((
                                                                                                                                 time.perf_counter() - self.start_time) // 60)))
                print("{} 共采集到 {} 条数据".format(school_name, len(df_result[school_name])))

                # 写入日志文件
                print("采集完毕，用时{}分{}秒。总用时{}分                            ".format(int(used_time // 60),
                                                                                            int(used_time % 60), int((
                                                                                                                                 time.perf_counter() - self.start_time) // 60)),
                      file=log_file)
                print("{} 共采集到 {} 条数据".format(school_name, len(df_result[school_name])), file=log_file)

            log_file.flush()  # 对写入的数据进行保存

        # 保存到Excel文件
        if school_name in df_result and len(df_result[school_name]) > 0:
            df_result[school_name].to_excel(self.output_file, index=False)
            print(f"结果已保存到 {self.output_file}")
            return df_result[school_name]
        else:
            return pd.DataFrame()

    async def run(self, limit=None):
        """
        运行校友动态采集流程

        Returns:
            DataFrame: 采集结果
        """
        print(f"开始采集{self.target_school}校友动态")

        # 获取校友名单
        alumni_df = LaungeeAPIUtils.get_alumni_df(self.target_school)

        # 如果指定了限制，则限制校友数量（优先使用传入的limit参数，其次使用初始化时的limit）
        effective_limit = limit if limit is not None else self.limit
        if effective_limit and isinstance(effective_limit, int) and effective_limit > 0:
            alumni_df = alumni_df[:effective_limit]

        if len(alumni_df) == 0:
            print(f"未获取到{self.target_school}的校友名单")
            return pd.DataFrame()

        print(f"获取到 {len(alumni_df)} 位校友信息")

        # 采集新闻
        results_df = await self.crawl_alumni_news(alumni_df)

        print(f"采集完成，共采集到 {len(results_df)} 条新闻")

        return results_df


def main(target_school='复旦大学', use_proxy=True, keep_days=14, addition_times=1, concurrent_limit=5, limit=None):
    """
    脚本的主入口点

    Args:
        target_school (str): 目标采集学校，默认为复旦大学
        use_proxy (bool): 是否使用阿布云代理池，默认为True
        keep_days (int): 保留天数，默认为14天
        addition_times (int): 补采次数，默认为1次
        concurrent_limit (int): 并发限制，默认为5
        limit (int, optional): 限制采集的校友人数，用于测试，默认为None（不限制）
    """
    collector = AlumniNewsCollector(
        target_school=target_school,
        use_proxy=use_proxy,
        keep_days=keep_days,
        addition_times=addition_times,
        concurrent_limit=concurrent_limit,
        limit=limit
    )


    results = asyncio.run(collector.run(limit=limit))

    print(f"采集完成，总用时: {int((time.perf_counter() - collector.start_time) // 60)}分钟")
    print(f"共采集到 {len(results)} 条新闻")
    return results


if __name__ == "__main__":
    import argparse
    import warnings
    import logging

    # 忽略 asyncio 相关警告
    warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
    
    # 禁用 asyncio 的调试日志
    logging.getLogger("asyncio").setLevel(logging.ERROR)

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='复旦大学校友动态采集器')
    parser.add_argument('--school', type=str, default='复旦大学', help='目标采集学校，默认为复旦大学')
    parser.add_argument('--no-proxy', action='store_true', help='不使用阿布云代理池')
    parser.add_argument('--keep-days', type=int, default=300, help='保留天数，默认为14天')
    parser.add_argument('--addition-times', type=int, default=1, help='补采次数，默认为1次')
    parser.add_argument('--concurrent-limit', type=int, default=5, help='并发限制，默认为5')
    parser.add_argument('--limit', type=int, default=5, help='限制采集的校友人数，用于测试，默认为None（不限制）')
    args = parser.parse_args()

    # 调用main函数，传递参数
    main(
        target_school=args.school,
        use_proxy=not args.no_proxy,
        keep_days=args.keep_days,
        addition_times=args.addition_times,
        concurrent_limit=args.concurrent_limit,
        limit=args.limit
    )


