# -*- coding: utf-8 -*-
"""
蓝矩API工具模块

本模块提供与蓝矩数据库API交互相关的功能，包括：
- JWT token生成
- 企业API接口调用
- 校友API接口调用
- 通用API请求处理
"""

import jwt
import json
import requests
import pandas as pd
from datetime import datetime


class LaungeeAPIUtils:
    """蓝矩API工具类，提供JWT token生成和各种API调用功能"""

    # 默认API密钥
    DEFAULT_API_KEY = 'GxSRnVWkF4doLBBHqpyMMu8pK8p9KOMv'

    # API端点
    ENTERPRISE_LIST_URL = "https://data.laungee.com/crawapi/getEntNameList"
    ENTERPRISE_NEWS_URL = "https://data.laungee.com/crawapi/insertEntNews"
    ALUMNI_LIST_URL = "https://data.laungee.com/crawapi/getXyNameList"

    @staticmethod
    def get_token(key=None):
        """
        为蓝矩数据库生成JWT认证令牌

        Args:
            key (str, optional): 用于生成JWT的密钥，默认使用DEFAULT_API_KEY

        Returns:
            str: 生成的JWT令牌
        """
        if key is None:
            key = LaungeeAPIUtils.DEFAULT_API_KEY

        payload = {
            'timestamp': int(datetime.now().timestamp() * 1000 - 300*1000),
            'iss': 'LaungeeData',
            'uid': 'crawlab'
        }
        token = jwt.encode(payload, key, algorithm='HS256')
        return token

    # ==================== 企业相关API ====================

    @staticmethod
    def get_enterprise_list(api_key=None):
        """
        从蓝矩API获取企业名称列表

        Args:
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            list: 包含'nameCn'和'enterpriseId'字段的企业字典列表
        """
        token = LaungeeAPIUtils.get_token(api_key)

        headers = {
            'Authorization': token,
        }

        response = requests.get(LaungeeAPIUtils.ENTERPRISE_LIST_URL, headers=headers)
        print(f"企业名称获取状态码：{response.status_code}")

        # 解析响应并返回企业列表
        response_data = json.loads(response.text)
        return response_data['data']

    @staticmethod
    def insert_enterprise_news(data, api_key=None):
        """
        向蓝矩API提交企业新闻数据

        Args:
            data (dict): 包含企业新闻信息的字典
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            Response: 请求响应对象
        """
        token = LaungeeAPIUtils.get_token(api_key)
        headers = {
            'Authorization': token,
        }
        response = requests.post(LaungeeAPIUtils.ENTERPRISE_NEWS_URL, headers=headers, json=data)
        return response

    # ==================== 校友相关API ====================

    @staticmethod
    def get_alumni_data(school_name, api_key=None):
        """
        从蓝矩API获取指定学校的校友数据

        Args:
            school_name (str): 要获取的学校名称
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            str: API返回的JSON字符串数据
        """
        token = LaungeeAPIUtils.get_token(api_key)

        payload = {'schoolName': school_name}
        headers = {
            'Authorization': token,
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        }

        response = requests.get(LaungeeAPIUtils.ALUMNI_LIST_URL, headers=headers, params=payload)
        print(f"校友数据获取状态码：{response.status_code}")

        return response.text

    @staticmethod
    def get_alumni_df(school_name, api_key=None):
        """
        获取指定学校的校友数据并转换为DataFrame格式（基础版本）
        用于特定要爬取的学校获取名单

        Args:
            school_name (str): 学校名称
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            DataFrame: 格式化后的校友数据DataFrame，包含列：ID, 姓名, 学校, 单位, 教育经历, 采集学校
        """
        data = LaungeeAPIUtils.get_alumni_data(school_name, api_key)

        try:
            df = pd.DataFrame(json.loads(data)['data'])
        except (json.JSONDecodeError, KeyError) as e:
            print(f"校友数据解析错误: {e}")
            return pd.DataFrame()

        if df.empty:
            return df

        # 填充空值并重命名列
        df = df.fillna(value='')
        df = df[['id', 'name', 'school', 'company', 'study']].rename(
            columns={'id': 'ID', 'name': '姓名', 'school': '学校', 'company': '单位', 'study': '教育经历'})

        # 设置采集学校
        df['采集学校'] = school_name

        # 处理学校和教育经历字段，去重并用分号连接
        df["学校"] = df["学校"].apply(lambda x: ';'.join(list(set(x))) if isinstance(x, list) else x)
        df["教育经历"] = df["教育经历"].apply(lambda x: ';'.join(list(set(x))) if isinstance(x, list) else x)

        return df

    @staticmethod
    def get_alumni_df_v2(school_name, served_schools, excluded_schools, api_key=None):
        """
        获取指定学校的校友数据并转换为DataFrame格式（高级版本）
        用于全面的学校数据获取，包含复杂的学校优先级逻辑处理

        处理逻辑：
        1. 已服务高校优先提取
        2. 在已服务高校中，复旦管院优先
        3. 排除不采集学校列表中的学校

        Args:
            school_name (str): 学校名称
            served_schools (list): 已服务学校列表
            excluded_schools (list): 不采集学校列表
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            DataFrame: 格式化后的校友数据DataFrame，包含列：ID, 姓名, 学校, 单位, 教育经历, 采集学校
        """
        data = LaungeeAPIUtils.get_alumni_data(school_name, api_key)

        try:
            df = pd.DataFrame(json.loads(data)['data'])
        except (json.JSONDecodeError, KeyError) as e:
            print(f"校友数据解析错误: {e}")
            return pd.DataFrame()

        if df.empty:
            return df

        # 填充空值并重命名列
        df = df.fillna(value='')
        df = df[['id', 'name', 'school', 'company', 'study']].rename(
            columns={'id': 'ID', 'name': '姓名', 'school': '学校', 'company': '单位', 'study': '教育经历'})

        # 已服务高校逻辑处理
        for df_i in range(len(df)):
            school_list = df.loc[df_i, '学校']  # 例：['复旦大学', '中国科学院大学']

            # 如果学校名单里有不采集高校名单里的值，那么采集学校设为空值
            if len(list(set(school_list) & set(excluded_schools))) > 0:
                df.loc[df_i, '采集学校'] = ''
            else:
                if len(school_list) > 0:
                    # 求交集：已服务学校与当前校友的学校列表
                    inter_school = list(set(school_list) & set(served_schools))

                    if len(inter_school) > 0:  # 有交集
                        # 如果交集中有复旦管院，优先选择复旦管院
                        if '复旦大学管理学院' in inter_school:
                            df.loc[df_i, '采集学校'] = '复旦大学管理学院'
                        else:
                            # 否则选择交集列表的第一个
                            df.loc[df_i, '采集学校'] = inter_school[0].replace(";", '')
                    else:  # 没有交集，选择学校列表的第一个
                        df.loc[df_i, '采集学校'] = school_list[0].replace(";", '')
                else:  # 如果学校列表是空的，那么采集学校为空
                    df.loc[df_i, '采集学校'] = ''

        # 处理学校和教育经历字段，去重并用分号连接
        df["学校"] = df["学校"].apply(lambda x: ';'.join(list(set(x))) if isinstance(x, list) else x)
        df["教育经历"] = df["教育经历"].apply(lambda x: ';'.join(list(set(x))) if isinstance(x, list) else x)

        return df

    # ==================== 通用API方法 ====================

    @staticmethod
    def make_api_request(url, method='GET', headers=None, params=None, json_data=None, api_key=None):
        """
        通用的API请求方法

        Args:
            url (str): API端点URL
            method (str): HTTP方法，默认为'GET'
            headers (dict, optional): 额外的请求头
            params (dict, optional): URL参数
            json_data (dict, optional): JSON数据（用于POST请求）
            api_key (str, optional): API密钥，默认使用DEFAULT_API_KEY

        Returns:
            Response: 请求响应对象
        """
        token = LaungeeAPIUtils.get_token(api_key)

        # 构建请求头
        request_headers = {
            'Authorization': token,
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        }

        if headers:
            request_headers.update(headers)

        # 发送请求
        if method.upper() == 'GET':
            response = requests.get(url, headers=request_headers, params=params)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=request_headers, params=params, json=json_data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")

        return response



