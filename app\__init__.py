# -*- coding: utf-8 -*-
"""
应用初始化模块

本模块负责创建和配置Flask应用实例，包括：
- 创建Flask应用
- 注册蓝图
- 配置错误处理
- 初始化扩展
"""

import os
from flask import Flask

def create_app(test_config=None):
    """
    创建并配置Flask应用实例
    
    Args:
        test_config (dict, optional): 测试配置，用于测试环境
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 创建Flask应用
    app = Flask(__name__, instance_relative_config=True)
    
    # 设置默认配置
    app.config.from_mapping(
        SECRET_KEY='dev',
        DATABASE=os.path.join(app.instance_path, 'app.sqlite'),
    )

    if test_config is None:
        # 加载实例配置（如果存在）
        app.config.from_pyfile('config.py', silent=True)
    else:
        # 加载测试配置
        app.config.from_mapping(test_config)

    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # 注册主蓝图
    from app.routes.routes import bp
    app.register_blueprint(bp)
    
    # 注册企业提取器蓝图
    from app.routes.enterprise_extractor import enterprise_extractor_bp
    app.register_blueprint(enterprise_extractor_bp)
    
    # 注册微信提取器蓝图
    from app.routes.wx_extractor import wx_extractor_bp
    app.register_blueprint(wx_extractor_bp)
    
    # 注册全局错误处理器
    @app.errorhandler(404)
    def page_not_found(e):
        from flask import jsonify
        return jsonify({
            "error": "页面不存在",
            "message": "请检查URL路径是否正确"
        }), 404
    
    @app.errorhandler(500)
    def internal_server_error(e):
        from flask import jsonify
        return jsonify({
            "error": "服务器内部错误",
            "message": "请稍后重试或联系管理员"
        }), 500
    
    return app
