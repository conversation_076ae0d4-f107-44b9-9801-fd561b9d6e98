[project]
name = "enterprise-dynamic-collection"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohappyeyeballs>=2.6.1",
    "aiohttp>=3.12.13",
    "beautifulsoup4>=4.13.4",
    "certifi>=2025.6.15",
    "charset-normalizer>=3.4.2",
    "debugpy>=1.8.14",
    "flask>=3.1.1",
    "flask-cors>=6.0.1",
    "gne>=0.3.1",
    "idna>=3.10",
    "json-repair>=0.47.7",
    "lxml>=6.0.0",
    "ntplib>=0.4.0",
    "numpy>=2.3.1",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pyjwt>=2.10.1",
    "pymongo>=4.13.2",
    "python-dateutil>=2.9.0.post0",
    "pytz>=2025.2",
    "requests>=2.32.4",
    "selenium>=4.34.0",
    "trio>=0.30.0",
    "trio-websocket>=0.12.2",
    "tzdata>=2025.2",
    "urllib3>=2.4.0",
]
