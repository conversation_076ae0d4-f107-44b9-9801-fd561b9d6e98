import os
import json
import requests
from openai import OpenAI
import re
import sys
import json

config_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if config_dir not in sys.path:
    sys.path.insert(0, config_dir)
from config.prompt_config import Config
config=Config()

def get_table_list_from_json(json_path,db_name):
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    # 直接返回所有表的完整内容
    db_items= data['schemaCollection'][0]['items']
    for  db in db_items:
        if db.get('name') == db_name:
            # 找到指定数据库，返回其所有表
            try:
                items = db['items']
                return items
            except KeyError:
                print(f"未找到数据库 {db_name} 的表信息")

def get_prompt(db_name):
    """
    获取指定数据库的提示词
    :param db_name: 数据库名称
    :return: 对应数据库的提示词
    """
    if db_name == "mongodb":
        return config.mongodb_prompt
    elif db_name == "oracle":
        return config.oracle_prompt
    # elif db_name == "mysql":
    #     return config.mysql_prompt
    # elif db_name == "postgresql":
    #     return config.postgresql_prompt
    else:
        raise ValueError(f"未知的数据库名称: {db_name}")
    
def call_deepseek_api(json_text, prompt, api_key):
    """
    调用 DeepSeek API，传入 json 文本和 prompt。
    :param json_text: json 字符串
    :param prompt: 提示词
    :param api_key: DeepSeek API 密钥
    :param api_url: DeepSeek API 地址
    :return: 返回 DeepSeek API 的响应内容
    """
    client = OpenAI(api_key=api_key, base_url="https://api.deepseek.com")
    response = client.chat.completions.create(
    model="deepseek-reasoner",
    messages=[
        {"role": "system", "content": prompt},
        {"role": "user", "content": json_text},
    ],
    stream=False
    )
    print()
    return response.choices[0].message.content
        


#这个好像没啥用
def update_field_description(json_path, table_name, field_name, new_description, new_title):
    """
    修改指定表的指定字段的description
    :param json_path: json文件路径
    :param table_name: 表名（如"MV_AI_KRM_ALUMNI_STUDY"）
    :param field_name: 字段名（如"KID"）
    :param new_description: 新的description内容
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    items = data['schemaCollection'][0]['items'][0]['items']
    for table in items:
        if table.get('name') == table_name:
            props = table['schema']['jsonSchema']['properties']
            if field_name in props:
                props[field_name]['description'] = new_description
                props[field_name]['title'] = new_title
                break
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

#这个也是
def main():
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    output_path = os.path.join(os.path.dirname(__file__), 'gmg.txt')
    with open(output_path, 'w', encoding='utf-8') as out_f:
        for filename in os.listdir(data_dir):
            if filename.endswith('.json'):
                json_path = os.path.join(data_dir, filename)
                table_list = get_table_list_from_json(json_path)
                print(f'File: {filename}')
                for idx, table in enumerate(table_list):
                    print(f"Table {idx + 1}: {table['name']}")
                    # 调用 DeepSeek API
                    print(f"Processing table: {table}")
                    json_text = json.dumps(table, ensure_ascii=False)
                    result = call_deepseek_api(json_text, prompt, "***********************************")
                    # out_f.write(f'File: {filename}, Table {idx + 1}: {table["name"]}\n')
                    # out_f.write(result + '\n\n')
                    result = re.sub(r'^```json\\s*|```$', '', result.strip(), flags=re.MULTILINE)
                    result = re.sub(r'^```|```$', '', result.strip(), flags=re.MULTILINE)
                    result = result.lstrip()
                    if result.startswith('json'):
                        result = result[4:].lstrip()
                    if not result.strip():
                        print(f"DeepSeek返回内容为空，跳过 {table['name']}")
                        continue
                    try:
                        field_list = json.loads(result).get('description', [])
                    except Exception as e:
                        print(f"JSON解析失败: {e}\n原始内容如下:\n{result}")
                        continue
                    dir = os.path.join(os.path.dirname(__file__), '..', 'data','Laungee.apifox.json')
                    for field in field_list:
                        field_name = field["field_name"]
                        field_description = field["field_description"]
                        field_title = field.get("field_title", "")
                        update_field_description(dir, table['name'], field_name, field_description, field_title)

if __name__ == '__main__':
    db_name = "mongodb"
    prompt = get_prompt(db_name)
    dir = os.path.join(os.path.dirname(__file__), '..', 'data', 'Laungee.apifox.json')
    output_dir = os.path.join(os.path.dirname(__file__), '..', 'output',db_name)
    os.makedirs(output_dir, exist_ok=True)
    table_list = get_table_list_from_json(dir,db_name)
    print(f"总共有{len(table_list)}个表")
    # gmg_list=["tyc_bak240715","tyc_bak240716","baidu_baike_bak240701","tyc","baidu_baike_csv_new","baidu_baike"]
    gmg_list=["baidu_baike"]
    for idx, table in enumerate(table_list):
        if(table['name'] not in gmg_list):   
            continue

        print(f"😊 正在处理第{idx+1}个表: {table['name']}")
        json_text = json.dumps(table, ensure_ascii=False)
        result = call_deepseek_api(json_text, prompt, "***********************************")
        print(f"DeepSeek返回结果: {result}")

        # 输出到以表名命名的json文件
        output_file = os.path.join(output_dir, f"{table['name']}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        print(f"😊 已保存到: {output_file}")
 

