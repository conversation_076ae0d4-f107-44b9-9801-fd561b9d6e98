import aiohttp
import asyncio
from lxml import etree
from urllib.parse import quote, urljoin
import random
from typing import List, Dict, Optional
from abc import ABC, abstractmethod
import datetime
import re
import sys
import os
import bs4

# 添加src/utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src', 'utils'))

# 导入工具类
from time_utils import TimeUtils
from content_utils import extract_content



class Searcher(ABC):
    """
    搜索器基类

    提供通用的搜索功能接口，包括HTTP请求、代理支持、错误处理等
    所有具体的搜索器实现都应该继承此基类
    """

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        """
        初始化搜索器

        Args:
            use_proxy (bool): 是否使用代理，默认False
            proxy_config (dict, optional): 代理配置字典，包含代理服务器信息
        """
        self.use_proxy = use_proxy
        self.proxy_config = proxy_config
        self.headers = self._get_headers()
        self.time_utils = TimeUtils()

    def _get_headers(self) -> Dict[str, str]:
        """
        获取HTTP请求头

        Returns:
            Dict[str, str]: 包含User-Agent等信息的请求头字典
        """
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    @abstractmethod
    def build_search_url(self, query: str, **kwargs) -> str:
        """构建搜索URL"""
        pass

    @abstractmethod
    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        """从HTML内容中提取搜索结果"""
        pass

    async def _make_async_request(self, url: str, max_retries: int = 3, proxy_operator=None) -> Optional[str]:
        """
        发送异步HTTP请求

        Args:
            url (str): 请求的URL地址
            max_retries (int): 最大重试次数，默认3次
            proxy_operator: 代理操作对象，用于代理池管理

        Returns:
            Optional[str]: 成功时返回响应文本内容，失败时返回None
        """
        for attempt in range(max_retries):
            try:
                # 随机延迟，避免请求过于频繁
                await asyncio.sleep(random.uniform(1, 3))

                async with aiohttp.ClientSession() as session:
                    request_kwargs = {
                        'url': url,
                        'headers': self.headers,
                        'timeout': aiohttp.ClientTimeout(total=30)  # 增加超时时间
                    }

                    print(f"正在请求URL: {url} (第{attempt + 1}次尝试)")

                    # 添加代理配置
                    if self.use_proxy and proxy_operator:
                        request_kwargs['proxy'] = proxy_operator.proxy['http']
                        print(f"使用代理: {proxy_operator.proxy['http']}")
                    elif self.use_proxy and self.proxy_config:
                        request_kwargs['proxy'] = self.proxy_config.get('http')
                        print(f"使用配置代理: {self.proxy_config.get('http')}")

                    async with session.get(**request_kwargs) as response:
                        print(f"响应状态码: {response.status}")

                        if response.status == 200:
                            # 检查是否遇到验证码页面
                            response_url = str(response.url)
                            if 'captcha' in response_url.lower() or 'verify' in response_url.lower():
                                print(f"遇到验证码页面，尝试重试 ({attempt + 1}/{max_retries})")
                                await asyncio.sleep(random.uniform(5, 10))
                                continue

                            content = await response.text()
                            print(f"成功获取内容，长度: {len(content)}")
                            return content
                        else:
                            print(f"HTTP状态码错误: {response.status}，尝试重试 ({attempt + 1}/{max_retries})")
                            await asyncio.sleep(random.uniform(3, 8))

            except asyncio.TimeoutError:
                print(f"请求超时，尝试重试 ({attempt + 1}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            except aiohttp.ClientHttpProxyError as e:
                if proxy_operator:
                    proxy_operator.check_proxy_pool()
                print(f"代理错误: {str(e)}，尝试重试 ({attempt + 1}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            except aiohttp.ClientError as e:
                print(f"客户端错误: {str(e)}，尝试重试 ({attempt + 1}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            except Exception as e:
                print(f"异步请求过程中出现未知错误: {str(e)}")
                await asyncio.sleep(random.uniform(2, 5))

        print(f"异步搜索失败，已重试 {max_retries} 次，URL: {url}")
        return None
    
    
    async def async_search(self, url: str, max_retries: int = 3, proxy_operator=None, **kwargs) -> List[Dict[str, str]]:
        """
        执行异步搜索

        Args:
            url (str): 要搜索的URL地址
            max_retries (int): 最大重试次数，默认3次
            proxy_operator: 代理操作对象，用于代理池管理
            **kwargs: 其他扩展参数，预留给子类使用

        Returns:
            List[Dict[str, str]]: 搜索结果列表，失败时返回空列表
        """
        try:
            print(f"开始异步搜索: {url}")

            # 发送HTTP请求获取页面内容
            html_content = await self._make_async_request(url, max_retries, proxy_operator)

            if html_content:
                print(f"成功获取页面内容，开始提取...")

                # 使用内容提取工具提取页面内容
                content, final_url = extract_content(html=html_content, actual_url=url)

                if content:
                    # 构建结果格式
                    result = {
                        'url': final_url,
                        'content': content,
                        'extracted_at': datetime.datetime.now().isoformat(),
                        'success': True
                    }
                    print(f"内容提取成功，内容长度: {len(content)}")
                    return [result]  # 返回列表格式保持一致性
                else:
                    print("内容提取失败，返回空结果")
                    return []
            else:
                print("未能获取页面内容")
                return []

        except Exception as e:
            print(f"异步搜索过程中出现错误: {str(e)}")
            return []

class EnterpriseNewsSearcher(Searcher):
    """
    企业新闻搜索器

    专门用于搜索企业相关新闻的搜索器实现
    继承自Searcher基类，针对企业新闻搜索进行优化
    """

    def __init__(self, use_proxy: bool = False, proxy_config: Optional[Dict] = None):
        """
        初始化企业新闻搜索器

        Args:
            use_proxy (bool): 是否使用代理
            proxy_config (dict): 代理配置
        """
        super().__init__(use_proxy, proxy_config)
        self.base_url = "https://www.baidu.com/s"

    def build_search_url(self, query: str, page: int = 0, time_range: str = "week") -> str:
        """
        构建百度新闻搜索URL

        Args:
            query (str): 搜索关键词
            page (int): 页码，默认0（第一页）
            time_range (str): 时间范围，默认"week"（暂未使用，预留扩展）

        Returns:
            str: 构建好的搜索URL
        """
        try:
            # 确保query是字符串，然后编码为UTF-8字节再进行URL编码
            if isinstance(query, str):
                encoded_query = quote(query.encode('utf-8'))
            else:
                encoded_query = quote(str(query).encode('utf-8'))

            # 构建完整的搜索URL
            base_url = "https://www.baidu.com/s"
            params = f"tn=news&word={encoded_query}"

            # 添加分页参数
            if page > 0:
                params += f"&pn={page * 10}"

            # 时间范围参数（预留，百度新闻可能需要特殊处理）
            # if time_range and time_range != "week":
            #     params += f"&gpc={time_range}"

            search_url = f"{base_url}?{params}"
            print(f"构建的搜索URL: {search_url}")
            return search_url

        except Exception as e:
            print(f"构建搜索URL时出错: {e}")
            # 返回一个默认的搜索URL
            return "https://www.baidu.com/s?tn=news&word=news"

    def extract_search_results(self, html_content: str) -> List[Dict[str, str]]:
        """
        从百度新闻搜索页面提取企业新闻结果

        Args:
            html_content (str): 页面HTML内容

        Returns:
            List[Dict[str, str]]: 搜索结果列表，每个结果包含标题、链接、摘要等信息
        """
        results = []
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 查找新闻结果容器（百度新闻的多种可能选择器）
            news_items = soup.find_all('div', class_='result') or \
                        soup.find_all('div', class_='result-op') or \
                        soup.find_all('div', class_='c-container')

            print(f"找到 {len(news_items)} 个新闻条目")

            for idx, item in enumerate(news_items):
                try:
                    # 提取标题和链接
                    title_elem = item.find('h3') or item.find('a')
                    if not title_elem:
                        continue

                    # 获取链接元素
                    if title_elem.name == 'a':
                        link_elem = title_elem
                    else:
                        link_elem = title_elem.find('a')

                    if not link_elem:
                        continue

                    title = link_elem.get_text(strip=True)
                    url = link_elem.get('href', '')

                    # 跳过空标题或链接
                    if not title or not url:
                        continue

                    # 提取摘要（多种可能的选择器）
                    summary_elem = item.find('span', class_='content-right_8Zs40') or \
                                  item.find('div', class_='c-abstract') or \
                                  item.find('span', class_='c-abstract') or \
                                  item.find('div', class_='c-span-last')

                    summary = summary_elem.get_text(strip=True) if summary_elem else ''

                    # 提取来源和时间信息
                    source_elem = item.find('span', class_='c-color-gray2') or \
                                 item.find('span', class_='c-showurl') or \
                                 item.find('cite') or \
                                 item.find('span', class_='c-color-gray')

                    source_info = source_elem.get_text(strip=True) if source_elem else ''

                    # 解析来源和发布时间
                    source = ''
                    publish_time = ''
                    if source_info:
                        # 使用时间工具解析时间
                        parsed_time = self.time_utils.parse_time(source_info)
                        if parsed_time:
                            publish_time = parsed_time
                            # 移除时间部分，剩余作为来源
                            source = re.sub(r'\d{4}-\d{1,2}-\d{1,2}.*', '', source_info).strip()
                        else:
                            # 如果没有解析到时间，整个作为来源
                            source = source_info

                    # 构建结果字典
                    result = {
                        'title': title,
                        'url': url,
                        'summary': summary,
                        'source': source,
                        'publish_time': publish_time,
                        'search_engine': '百度新闻',
                        'search_type': 'enterprise_news',
                        'extracted_at': datetime.datetime.now().isoformat(),
                        'index': idx + 1
                    }

                    results.append(result)
                    print(f"成功提取第 {idx + 1} 个结果: {title[:50]}...")

                except Exception as e:
                    print(f"提取第 {idx + 1} 个企业新闻结果时出错: {e}")
                    continue

            print(f"总共成功提取 {len(results)} 个新闻结果")

        except Exception as e:
            print(f"解析企业新闻搜索结果时出错: {e}")
            import traceback
            traceback.print_exc()

        return results
    
if __name__ == '__main__':
    """
    测试代码
    用于验证搜索器功能是否正常
    """
    print("开始测试企业新闻搜索器...")

    # 创建搜索器实例
    searcher = EnterpriseNewsSearcher()

    # 测试URL
    test_url = "https://mp.weixin.qq.com/s?src=11&timestamp=1751877042&ver=6097&signature=vN1HVBGsDM-wc1krpSFiTMHf*t2gDfPHRvJ2n3hRNMs5XCfWxlg*cUz7jAkY*-pJ16wlyXyUOKMOduao9-9H5MNDMT4fpShEGG7hLYagY3JhASwLhVJAJWkbN1ia12S5&new=1"

    try:
        # 执行异步搜索
        results = asyncio.run(searcher.async_search(test_url))

        print(f"\n搜索完成！")
        print(f"结果数量: {len(results)}")

        if results:
            for i, result in enumerate(results, 1):
                print(f"\n--- 结果 {i} ---")
                print(f"URL: {result.get('url', 'N/A')}")
                print(f"内容长度: {len(result.get('content', ''))}")
                print(f"提取时间: {result.get('extracted_at', 'N/A')}")
                print(f"成功状态: {result.get('success', False)}")
        else:
            print("未获取到任何结果")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
