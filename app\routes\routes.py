# -*- coding: utf-8 -*-
"""
主路由模块

提供应用的基础路由和API接口
"""

from flask import Blueprint, jsonify, request

# 创建主路由蓝图
bp = Blueprint('main', __name__)


@bp.route('/')
def home():
    """
    主页接口

    返回应用的基本信息
    """
    return jsonify({
        "message": "欢迎使用Flask企业提取器API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "企业搜索": "/enterprise/search",
            "批量企业搜索": "/enterprise/batch-search",
            "企业功能测试": "/enterprise/test",
            "微信内容提取": "/wx/extract",
            "批量微信提取": "/wx/batch-extract",
            "微信功能测试": "/wx/test",
            "API文档": "/api/docs"
        }
    })


@bp.route('/health')
def health_check():
    """
    健康检查接口

    用于检查应用是否正常运行
    """
    return jsonify({
        "status": "healthy",
        "timestamp": "2024-01-01T00:00:00Z"
    })


@bp.route('/api/info')
def api_info():
    """
    API信息接口

    返回API的详细信息
    """
    return jsonify({
        "api_name": "Flask企业提取器API",
        "version": "1.0.0",
        "description": "基于Selenium的企业信息提取和搜索API",
        "features": [
            "企业新闻搜索",
            "微信内容提取",
            "批量URL处理",
            "内容提取",
            "异步处理",
            "代理支持"
        ],
        "endpoints": {
            "GET /": "主页",
            "GET /health": "健康检查",
            "GET /api/info": "API信息",
            "GET|POST /enterprise/search": "企业搜索",
            "POST /enterprise/batch-search": "批量企业搜索",
            "GET /enterprise/test": "企业功能测试",
            "GET|POST /wx/extract": "微信内容提取",
            "POST /wx/batch-extract": "批量微信提取",
            "GET /wx/test": "微信功能测试"
        }
    })


@bp.route('/api/docs')
def api_docs():
    """
    API文档接口

    返回简化的API使用说明
    """
    docs = {
        "title": "Flask企业提取器API文档",
        "base_url": "http://localhost:5000",
        "authentication": "无需认证",
        "content_type": "application/json",
        "endpoints": [
            {
                "path": "/enterprise/search",
                "method": "GET|POST",
                "description": "企业搜索接口",
                "parameters": {
                    "url": "搜索URL（必填）",
                    "use_proxy": "是否使用代理（可选，默认false）",
                    "max_retries": "最大重试次数（可选，默认3）"
                },
                "example": {
                    "url": "https://www.baidu.com/s?tn=news&word=腾讯",
                    "use_proxy": False,
                    "max_retries": 3
                }
            },
            {
                "path": "/enterprise/batch-search",
                "method": "POST",
                "description": "批量企业搜索接口",
                "parameters": {
                    "urls": "URL列表（必填）",
                    "use_proxy": "是否使用代理（可选）",
                    "max_retries": "最大重试次数（可选）",
                    "concurrent_limit": "并发限制（可选，默认3）"
                },
                "example": {
                    "urls": [
                        "https://www.baidu.com/s?tn=news&word=腾讯",
                        "https://www.baidu.com/s?tn=news&word=阿里巴巴"
                    ],
                    "concurrent_limit": 2
                }
            },
            {
                "path": "/enterprise/test",
                "method": "GET",
                "description": "企业功能测试接口",
                "parameters": "无",
                "example": "GET /enterprise/test"
            },
            {
                "path": "/wx/extract",
                "method": "GET|POST",
                "description": "微信内容提取接口",
                "parameters": {
                    "url": "微信文章URL（必填）",
                    "use_proxy": "是否使用代理（可选，默认false）",
                    "max_retries": "最大重试次数（可选，默认3）"
                },
                "example": {
                    "url": "https://mp.weixin.qq.com/s/example123",
                    "use_proxy": False,
                    "max_retries": 3
                }
            },
            {
                "path": "/wx/batch-extract",
                "method": "POST",
                "description": "批量微信内容提取接口",
                "parameters": {
                    "urls": "微信URL列表（必填）",
                    "use_proxy": "是否使用代理（可选）",
                    "max_retries": "最大重试次数（可选）",
                    "concurrent_limit": "并发限制（可选，默认2）"
                },
                "example": {
                    "urls": [
                        "https://mp.weixin.qq.com/s/example1",
                        "https://mp.weixin.qq.com/s/example2"
                    ],
                    "concurrent_limit": 2
                }
            },
            {
                "path": "/wx/test",
                "method": "GET",
                "description": "微信功能测试接口",
                "parameters": "无",
                "example": "GET /wx/test"
            }
        ]
    }

    return jsonify(docs)


@bp.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "error": "接口不存在",
        "message": "请检查URL路径是否正确",
        "available_endpoints": [
            "/",
            "/health",
            "/api/info",
            "/api/docs",
            "/enterprise/search",
            "/enterprise/batch-search",
            "/enterprise/test",
            "/wx/extract",
            "/wx/batch-extract",
            "/wx/test"
        ]
    }), 404


@bp.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        "error": "服务器内部错误",
        "message": "请稍后重试或联系管理员"
    }), 500