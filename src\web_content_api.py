"""
网页内容提取Flask API接口
提供RESTful API接口来调用WebContentExtractor的功能
"""

import os
import sys
import json
import traceback
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# 将项目根目录添加到路径
root_path = os.path.abspath(os.path.join(os.path.abspath(__file__), '..', '..'))
sys.path.append(root_path)

from src.web_content_extractor import WebContentExtractor

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 全局变量存储提取器实例
extractor = None

def init_extractor():
    """
    初始化WebContentExtractor实例
    
    Returns:
        WebContentExtractor: 初始化的提取器实例
    """
    global extractor
    if extractor is None:
        try:
            # 生成输出文件名
            output_filename = f"API网页内容提取结果_{datetime.now().strftime('%Y-%m-%d %H-%M-%S')}.xlsx"
            
            # 创建提取器实例
            extractor = WebContentExtractor(
                output_filename,
                use_proxy=False,
                batch_size=10,
                mongo_collection="api_web_content_data",
                dify_api_url="http://192.168.1.213/v1",  # 本地Dify实例URL
                dify_api_key="app-ftM9txPiU8gFZ9cMMRe3kB57",  # 替换为实际的API密钥
                enable_dify_analysis=True,  # 启用Dify分析
                thread_count=2  # API模式使用较少线程数
            )
            print(f"WebContentExtractor初始化成功，输出文件: {output_filename}")
            
        except Exception as e:
            print(f"初始化WebContentExtractor失败: {str(e)}")
            traceback.print_exc()
            extractor = None
    
    return extractor

@app.route('/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    
    Returns:
        dict: 健康状态信息
    """
    try:
        current_extractor = init_extractor()
        if current_extractor:
            # 获取驱动状态
            driver_status = current_extractor.get_driver_status()
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'extractor_initialized': True,
                'driver_status': driver_status
            })
        else:
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'extractor_initialized': False,
                'error': 'WebContentExtractor初始化失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'error': str(e)
        }), 500

@app.route('/extract', methods=['POST'])
def extract_web_content():
    """
    网页内容提取接口
    
    请求参数:
        url (str): 要提取内容的网页URL，必填
        enterprise_name (str): 企业名称，可选
        enterprise_id (str): 企业ID，可选
    
    Returns:
        dict: 提取结果，包含网页内容、PDF文件名、实际访问URL和Dify分析结果
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空，需要JSON格式数据'
            }), 400
        
        # 验证必填参数
        url = data.get('url', '').strip()
        if not url:
            return jsonify({
                'success': False,
                'error': 'URL参数不能为空'
            }), 400
        
        # 获取可选参数
        enterprise_name = data.get('enterprise_name', '').strip()
        enterprise_id = data.get('enterprise_id', '').strip()
        
        # 初始化提取器
        current_extractor = init_extractor()
        if not current_extractor:
            return jsonify({
                'success': False,
                'error': 'WebContentExtractor初始化失败'
            }), 500
        
        # 调用提取方法
        result = current_extractor.process_single_url_api(url, enterprise_name, enterprise_id)
        
        # 添加时间戳
        result['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 根据成功状态返回相应的HTTP状态码
        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 500
            
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        
        return jsonify({
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/batch_extract', methods=['POST'])
def batch_extract_web_content():
    """
    批量网页内容提取接口
    
    请求参数:
        urls (list): URL列表，每个元素包含url、enterprise_name、enterprise_id字段
    
    Returns:
        dict: 批量提取结果
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空，需要JSON格式数据'
            }), 400
        
        # 验证参数
        urls = data.get('urls', [])
        if not urls or not isinstance(urls, list):
            return jsonify({
                'success': False,
                'error': 'urls参数必须是非空列表'
            }), 400
        
        # 初始化提取器
        current_extractor = init_extractor()
        if not current_extractor:
            return jsonify({
                'success': False,
                'error': 'WebContentExtractor初始化失败'
            }), 500
        
        # 批量处理URL
        results = []
        for i, url_data in enumerate(urls):
            if not isinstance(url_data, dict):
                results.append({
                    'success': False,
                    'error': f'第{i+1}个URL数据格式错误，必须是字典类型'
                })
                continue
            
            url = url_data.get('url', '').strip()
            if not url:
                results.append({
                    'success': False,
                    'error': f'第{i+1}个URL不能为空'
                })
                continue
            
            enterprise_name = url_data.get('enterprise_name', '').strip()
            enterprise_id = url_data.get('enterprise_id', '').strip()
            
            # 处理单个URL
            result = current_extractor.process_single_url_api(url, enterprise_name, enterprise_id)
            results.append(result)
        
        # 统计结果
        success_count = sum(1 for r in results if r.get('success', False))
        total_count = len(results)
        
        return jsonify({
            'success': True,
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': total_count - success_count,
            'results': results,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 200
        
    except Exception as e:
        error_msg = f"批量处理请求时出错: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        
        return jsonify({
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.route('/status', methods=['GET'])
def get_status():
    """
    获取提取器状态信息
    
    Returns:
        dict: 状态信息
    """
    try:
        current_extractor = init_extractor()
        if current_extractor:
            driver_status = current_extractor.get_driver_status()
            return jsonify({
                'success': True,
                'extractor_initialized': True,
                'driver_status': driver_status,
                'dify_analysis_enabled': current_extractor.enable_dify_analysis,
                'mongo_collection': current_extractor.mongo_collection,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'extractor_initialized': False,
                'error': 'WebContentExtractor未初始化',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': '服务器内部错误',
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }), 500

def cleanup():
    """清理资源"""
    global extractor
    if extractor:
        try:
            extractor.close()
            print("WebContentExtractor资源已清理")
        except Exception as e:
            print(f"清理资源时出错: {str(e)}")
        finally:
            extractor = None

if __name__ == '__main__':
    import atexit
    
    # 注册退出时的清理函数
    atexit.register(cleanup)
    
    try:
        print("启动网页内容提取API服务...")
        print("API接口说明:")
        print("  GET  /health - 健康检查")
        print("  GET  /status - 获取状态信息")
        print("  POST /extract - 单个URL内容提取")
        print("  POST /batch_extract - 批量URL内容提取")
        print("-" * 50)
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,       # 端口号
            debug=False,     # 生产环境关闭调试模式
            threaded=True    # 启用多线程支持
        )
        
    except KeyboardInterrupt:
        print("\n接收到中断信号，正在关闭服务...")
        cleanup()
    except Exception as e:
        print(f"启动API服务时出错: {str(e)}")
        traceback.print_exc()
        cleanup()
